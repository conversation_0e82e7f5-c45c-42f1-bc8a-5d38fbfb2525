package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"

	"hospital-management/internal/models"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 未找到.env文件，将使用环境变量")
	}

	// 连接数据库
	db, err := connectDB()
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 删除所有表
	dropTables(db)

	// 创建表
	createTables(db)

	// 插入示例数据
	insertSampleData(db)

	fmt.Println("数据库重置完成！")
}

// 连接数据库
func connectDB() (*gorm.DB, error) {
	host := os.Getenv("DB_HOST")
	if host == "" {
		host = "*************" // 使用您的实际数据库主机
	}

	port := os.Getenv("DB_PORT")
	if port == "" {
		port = "5432"
	}

	user := os.Getenv("DB_USER")
	if user == "" {
		user = "zhikong" // 使用您的实际数据库用户名
	}

	password := os.Getenv("DB_PASSWORD")
	if password == "" {
		password = "196717myh" // 使用您的实际数据库密码
	}

	dbname := os.Getenv("DB_NAME")
	if dbname == "" {
		dbname = "postgres" // 使用您的实际数据库名称
	}

	sslmode := os.Getenv("DB_SSLMODE")
	if sslmode == "" {
		sslmode = "disable"
	}

	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		host, port, user, password, dbname, sslmode,
	)

	fmt.Println("正在连接数据库...")
	fmt.Printf("DSN: %s\n", dsn)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
	})

	if err != nil {
		return nil, err
	}

	// 设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(10)
	// 设置最大打开连接数
	sqlDB.SetMaxOpenConns(100)
	// 设置连接最大生存时间
	sqlDB.SetConnMaxLifetime(time.Hour)

	fmt.Println("数据库连接成功")
	return db, nil
}

// 删除所有表
func dropTables(db *gorm.DB) {
	fmt.Println("正在删除所有表...")
	
	// 禁用外键约束（PostgreSQL）
	db.Exec("SET session_replication_role = 'replica';")
	
	// 获取所有表名
	var tables []string
	db.Raw(`SELECT tablename FROM pg_tables WHERE schemaname = 'public'`).Scan(&tables)
	
	fmt.Printf("找到 %d 个表需要删除\n", len(tables))
	
	// 删除所有表
	for _, table := range tables {
		fmt.Printf("正在删除表: %s\n", table)
		db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS \"%s\" CASCADE;", table))
	}
	
	// 恢复外键约束
	db.Exec("SET session_replication_role = 'origin';")
	
	fmt.Println("所有表已删除")
}

// 创建表
func createTables(db *gorm.DB) {
	fmt.Println("正在创建表结构...")
	
	// 自动迁移表结构
	err := db.AutoMigrate(
		&models.Department{},
		&models.User{},
		&models.BudgetItem{},
		&models.BudgetFreeze{},
		&models.ExpenseType{},
		&models.ExpenseClaim{},
		&models.ExpenseClaimDetail{},
		&models.WorkflowDefinition{},
		&models.WorkflowNode{},
		&models.WorkflowInstance{},
		&models.WorkflowApprovalHistory{},
	)
	
	if err != nil {
		log.Fatalf("创建表失败: %v", err)
	}
	
	fmt.Println("所有表已创建")
}

// 插入示例数据
func insertSampleData(db *gorm.DB) {
	fmt.Println("正在插入示例数据...")
	
	// 创建部门
	deptID := uuid.New()
	dept := models.Department{
		BaseModel: models.BaseModel{
			ID:        deptID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:   "信息科技部",
		Code:   "IT",
		Level:  1,
		Sort:   1,
		Status: "active",
	}
	
	if err := db.Create(&dept).Error; err != nil {
		log.Fatalf("创建部门失败: %v", err)
	}
	fmt.Println("部门数据已插入")
	
	// 创建用户
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	userID := uuid.New()
	user := models.User{
		BaseModel: models.BaseModel{
			ID:        userID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Username:     "admin",
		PasswordHash: string(hashedPassword),
		FullName:     "系统管理员",
		DepartmentID: deptID,
		EmployeeID:   "EMP001",
		Title:        "部门负责人",
		Roles:        []string{"admin", "department_head"},
		Avatar:       "https://randomuser.me/api/portraits/men/1.jpg",
		Email:        "<EMAIL>",
		Phone:        "13800138000",
		Status:       "active",
	}
	
	if err := db.Create(&user).Error; err != nil {
		log.Fatalf("创建用户失败: %v", err)
	}
	fmt.Println("用户数据已插入")
	
	// 创建预算项
	budgetItemID := uuid.New()
	budgetItem := models.BudgetItem{
		BaseModel: models.BaseModel{
			ID:        budgetItemID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Year:         time.Now().Year(),
		DepartmentID: deptID,
		SubjectCode:  "IT-2023-001",
		SubjectName:  "信息系统维护费",
		TotalAmount:  1000000.00,
		UsedAmount:   650000.00,
		FrozenAmount: 80000.00,
		ControlType:  "rigid",
		Status:       "active",
	}
	
	if err := db.Create(&budgetItem).Error; err != nil {
		log.Fatalf("创建预算项失败: %v", err)
	}
	fmt.Println("预算项数据已插入")
	
	// 创建预算冻结记录
	budgetFreezeID := uuid.New()
	businessID := uuid.New() // 模拟业务ID
	budgetFreeze := models.BudgetFreeze{
		BaseModel: models.BaseModel{
			ID:        budgetFreezeID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		BudgetItemID: budgetItemID,
		Amount:       80000.00,
		BusinessID:   businessID,
		BusinessType: "pre_approval",
		Status:       "active",
	}
	
	if err := db.Create(&budgetFreeze).Error; err != nil {
		log.Fatalf("创建预算冻结记录失败: %v", err)
	}
	fmt.Println("预算冻结记录已插入")
	
	// 创建费用类型
	expenseTypeID := uuid.New()
	expenseType := models.ExpenseType{
		BaseModel: models.BaseModel{
			ID:        expenseTypeID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:        "办公用品",
		Code:        "OFFICE",
		Description: "办公用品费用",
		Status:      "active",
	}
	
	if err := db.Create(&expenseType).Error; err != nil {
		log.Fatalf("创建费用类型失败: %v", err)
	}
	fmt.Println("费用类型数据已插入")
	
	// 创建工作流定义
	workflowDefID := uuid.New()
	workflowDef := models.WorkflowDefinition{
		BaseModel: models.BaseModel{
			ID:        workflowDefID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Key:         "expense_claim_flow",
		Name:        "报销审批流程",
		Description: "标准报销审批流程",
		Version:     1,
		Status:      "active",
	}
	
	if err := db.Create(&workflowDef).Error; err != nil {
		log.Fatalf("创建工作流定义失败: %v", err)
	}
	fmt.Println("工作流定义数据已插入")
	
	// 创建工作流节点
	startNodeID := uuid.New()
	startNode := models.WorkflowNode{
		BaseModel: models.BaseModel{
			ID:        startNodeID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		DefinitionID: workflowDefID,
		NodeID:       "start",
		NodeType:     "start",
		NodeName:     "开始",
		NextNodes:    "dept_head",
	}
	
	if err := db.Create(&startNode).Error; err != nil {
		log.Fatalf("创建工作流节点失败: %v", err)
	}
	
	deptHeadNodeID := uuid.New()
	deptHeadNode := models.WorkflowNode{
		BaseModel: models.BaseModel{
			ID:        deptHeadNodeID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		DefinitionID: workflowDefID,
		NodeID:       "dept_head",
		NodeType:     "approval",
		NodeName:     "部门负责人审批",
		NextNodes:    "finance",
		Assignee:     "department_head:" + deptID.String(),
	}
	
	if err := db.Create(&deptHeadNode).Error; err != nil {
		log.Fatalf("创建工作流节点失败: %v", err)
	}
	fmt.Println("工作流节点数据已插入")
	
	// 创建报销单
	claimID := uuid.New()
	claim := models.ExpenseClaim{
		BaseModel: models.BaseModel{
			ID:        claimID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Title:       "办公用品采购报销",
		ApplicantID: userID,
		DepartmentID: deptID,
		TotalAmount: 1500.00,
		Status:      "pending",
		PayeeType:   "personal",
		PayeeInfo:   []byte(`{"name":"系统管理员","bank":"中国银行","account":"6222020000000000000"}`),
		SubmittedAt: func() *time.Time { t := time.Now(); return &t }(),
	}
	
	if err := db.Create(&claim).Error; err != nil {
		log.Fatalf("创建报销单失败: %v", err)
	}
	fmt.Println("报销单数据已插入")
	
	// 创建报销单明细
	claimDetailID := uuid.New()
	claimDetail := models.ExpenseClaimDetail{
		BaseModel: models.BaseModel{
			ID:        claimDetailID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		ClaimID:       claimID,
		ExpenseTypeID: expenseTypeID,
		BudgetItemID:  budgetItemID,
		Amount:        1500.00,
		Description:   "购买办公用品",
		InvoiceData:   []byte(`{"code":"********90********","number":"********","date":"2023-01-15","amount":1500.00}`),
	}
	
	if err := db.Create(&claimDetail).Error; err != nil {
		log.Fatalf("创建报销单明细失败: %v", err)
	}
	fmt.Println("报销单明细数据已插入")
	
	// 创建工作流实例
	instanceID := uuid.New()
	instance := models.WorkflowInstance{
		BaseModel: models.BaseModel{
			ID:        instanceID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		DefinitionKey: "expense_claim_flow",
		BusinessID:    claimID,
		BusinessType:  "expense_claim",
		CurrentNodeID: "dept_head",
		Status:        "active",
		StartUserID:   userID,
		StartTime:     time.Now(),
	}
	
	if err := db.Create(&instance).Error; err != nil {
		log.Fatalf("创建工作流实例失败: %v", err)
	}
	fmt.Println("工作流实例数据已插入")
	
	// 创建工作流审批历史
	historyID := uuid.New()
	history := models.WorkflowApprovalHistory{
		BaseModel: models.BaseModel{
			ID:        historyID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		InstanceID: instanceID,
		NodeID:     "start",
		NodeName:   "开始",
		ApproverID: userID,
		Action:     "submit",
		Comment:    "提交报销申请",
	}
	
	if err := db.Create(&history).Error; err != nil {
		log.Fatalf("创建工作流审批历史失败: %v", err)
	}
	fmt.Println("工作流审批历史数据已插入")
}