package main

import (
	"fmt"
	"log"

	"hospital-management/internal/config"
	"hospital-management/internal/database"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("警告: 无法加载环境配置文件: %v", err)
	}

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取底层数据库连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	fmt.Println("=== 数据库表验证 ===")

	// 查询所有表
	var tables []string
	if err := db.Raw("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE' ORDER BY table_name").Scan(&tables).Error; err != nil {
		log.Fatalf("查询表失败: %v", err)
	}

	fmt.Printf("数据库中共有 %d 个表:\n", len(tables))
	for i, table := range tables {
		fmt.Printf("%d. %s\n", i+1, table)
	}

	// 检查每个表的记录数
	fmt.Println("\n=== 表记录统计 ===")
	for _, table := range tables {
		var count int64
		if err := db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count).Error; err != nil {
			fmt.Printf("%-30s: 查询失败 (%v)\n", table, err)
		} else {
			fmt.Printf("%-30s: %d 条记录\n", table, count)
		}
	}

	// 验证关键数据
	fmt.Println("\n=== 关键数据验证 ===")
	
	// 检查部门数据
	var deptCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_departments").Scan(&deptCount)
	fmt.Printf("部门数量: %d\n", deptCount)

	// 检查用户数据
	var userCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_users").Scan(&userCount)
	fmt.Printf("用户数量: %d\n", userCount)

	// 检查角色数据
	var roleCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_roles").Scan(&roleCount)
	fmt.Printf("角色数量: %d\n", roleCount)

	// 检查预算数据
	var budgetCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_budget_schemes").Scan(&budgetCount)
	fmt.Printf("预算方案数量: %d\n", budgetCount)

	// 检查供应商数据
	var supplierCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_suppliers").Scan(&supplierCount)
	fmt.Printf("供应商数量: %d\n", supplierCount)

	// 检查合同数据
	var contractCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_contracts").Scan(&contractCount)
	fmt.Printf("合同数量: %d\n", contractCount)

	// 检查资产分类数据
	var assetCategoryCount int64
	db.Raw("SELECT COUNT(*) FROM tbl_asset_categories").Scan(&assetCategoryCount)
	fmt.Printf("资产分类数量: %d\n", assetCategoryCount)

	fmt.Println("\n=== 验证完成 ===")
	fmt.Println("数据库迁移成功！所有表和初始数据已创建。")
}
