package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type AuthController struct {
	authService *services.AuthService
	validator   *validator.Validate
}

func NewAuthController(authService *services.AuthService) *AuthController {
	return &AuthController{
		authService: authService,
		validator:   customValidator.NewValidator(),
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags 认证授权
// @Accept json
// @Produce json
// @Param request body dto.LoginRequest true "登录请求"
// @Success 200 {object} dto.LoginResponse "登录成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "认证失败"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/login [post]
func (c *AuthController) Login(ctx *gin.Context) {
	var req dto.LoginRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "参数错误: " + err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("参数验证失败:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "参数验证失败: " + err.Error(),
		})
		return
	}

	// 调用服务
	responseData, err := c.authService.Login(&req)
	if err != nil {
		logger.Error("登录失败:", err)
		if err.Error() == "用户名或密码错误" {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"error": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "登录失败: " + err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, responseData)
}

// RefreshToken 刷新访问令牌
// @Summary 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证授权
// @Accept json
// @Produce json
// @Param request body map[string]string true "刷新令牌请求"
// @Success 200 {object} map[string]string "刷新成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "令牌无效"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/auth/refresh [post]
func (c *AuthController) RefreshToken(ctx *gin.Context) {
	var req struct {
		Token string `json:"token" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "参数错误: " + err.Error(),
		})
		return
	}

	// 调用服务
	newToken, err := c.authService.RefreshToken(req.Token)
	if err != nil {
		logger.Error("刷新令牌失败:", err)
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "刷新令牌失败: " + err.Error(),
		})
		return
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, map[string]string{
		"token": newToken,
	})
}

// GetProfile 获取当前用户信息
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 认证授权
// @Accept json
// @Produce json
// @Success 200 {object} dto.UserResponse "获取成功"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /api/v1/auth/profile [get]
func (c *AuthController) GetProfile(ctx *gin.Context) {
	// 获取当前用户ID
	userIDStr, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "用户ID格式错误",
		})
		return
	}

	// 调用服务
	userInfo, err := c.authService.GetUserInfo(userID)
	if err != nil {
		logger.Error("获取用户信息失败:", err)
		if err.Error() == "用户不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"error": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "获取用户信息失败: " + err.Error(),
			})
		}
		return
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, userInfo)
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户的密码
// @Tags 认证授权
// @Accept json
// @Produce json
// @Param request body dto.ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} map[string]interface{} "修改成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /api/v1/auth/change-password [post]
func (c *AuthController) ChangePassword(ctx *gin.Context) {
	var req dto.ChangePasswordRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "参数错误: " + err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("参数验证失败:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "参数验证失败: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userIDStr, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "用户ID格式错误",
		})
		return
	}

	// 调用服务
	if err := c.authService.ChangePassword(userID, &req); err != nil {
		logger.Error("修改密码失败:", err)
		if err.Error() == "原密码错误" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "修改密码失败: " + err.Error(),
			})
		}
		return
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, map[string]string{
		"message": "密码修改成功",
	})
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出（客户端清除令牌）
// @Tags 认证授权
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "登出成功"
// @Security BearerAuth
// @Router /api/v1/auth/logout [post]
func (c *AuthController) Logout(ctx *gin.Context) {
	// 获取当前用户ID
	userIDStr, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "用户ID格式错误",
		})
		return
	}

	// 调用服务
	if err := c.authService.Logout(userID); err != nil {
		logger.Error("登出失败:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "登出失败: " + err.Error(),
		})
		return
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, map[string]string{
		"message": "登出成功",
	})
}