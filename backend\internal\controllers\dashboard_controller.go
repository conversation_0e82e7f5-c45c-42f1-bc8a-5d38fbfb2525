package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type DashboardController struct {
	dashboardService *services.DashboardService
}

func NewDashboardController(dashboardService *services.DashboardService) *DashboardController {
	return &DashboardController{
		dashboardService: dashboardService,
	}
}

// GetDepartmentHeadDashboard 获取部门负责人工作台数据
// @Summary 获取部门负责人工作台数据
// @Description 获取部门负责人工作台所需的预算概览和待审批列表
// @Tags 工作台
// @Accept json
// @Produce json
// @Param year query int false "年份（可选，默认为当前年份）"
// @Success 200 {object} dto.DashboardOverviewResponse "获取成功"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /api/v1/dashboard/dept-head [get]
func (c *DashboardController) GetDepartmentHeadDashboard(ctx *gin.Context) {
	// 获取当前用户ID和部门ID
	userIDStr, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "用户ID格式错误",
		})
		return
	}

	departmentIDStr, exists := ctx.Get("department_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	departmentID, err := uuid.Parse(departmentIDStr.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "部门ID格式错误",
		})
		return
	}

	// 获取查询参数
	yearStr := ctx.DefaultQuery("year", strconv.Itoa(time.Now().Year()))
	year, err := strconv.Atoi(yearStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "年份参数格式错误",
		})
		return
	}

	// 获取预算概览
	budgetOverview, err := c.dashboardService.GetDepartmentBudgetOverview(departmentID, year)
	if err != nil {
		logger.Error("获取部门预算概览失败:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取部门预算概览失败: " + err.Error(),
		})
		return
	}

	// 获取待审批列表
	pendingApprovals, err := c.dashboardService.GetPendingApprovals(userID, 10)
	if err != nil {
		logger.Error("获取待审批列表失败:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取待审批列表失败: " + err.Error(),
		})
		return
	}

	// 构建响应
	response := dto.DashboardOverviewResponse{
		BudgetOverview:   *budgetOverview,
		PendingApprovals: pendingApprovals,
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, response)
}

// GetPendingApprovals 获取待审批列表
// @Summary 获取待审批列表
// @Description 获取当前用户的待审批列表
// @Tags 工作台
// @Accept json
// @Produce json
// @Param limit query int false "返回数量限制（可选，默认为10）"
// @Success 200 {array} dto.ApprovalItemResponse "获取成功"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /api/v1/dashboard/pending-approvals [get]
func (c *DashboardController) GetPendingApprovals(ctx *gin.Context) {
	// 获取当前用户ID
	userIDStr, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "用户ID格式错误",
		})
		return
	}

	// 获取查询参数
	limitStr := ctx.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "limit参数格式错误",
		})
		return
	}

	// 获取待审批列表
	pendingApprovals, err := c.dashboardService.GetPendingApprovals(userID, limit)
	if err != nil {
		logger.Error("获取待审批列表失败:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取待审批列表失败: " + err.Error(),
		})
		return
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, pendingApprovals)
}

// GetBudgetOverview 获取预算概览
// @Summary 获取预算概览
// @Description 获取部门预算概览
// @Tags 工作台
// @Accept json
// @Produce json
// @Param departmentId query string false "部门ID（可选，默认为当前用户部门）"
// @Param year query int false "年份（可选，默认为当前年份）"
// @Success 200 {object} dto.BudgetOverviewResponse "获取成功"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /api/v1/dashboard/budget-overview [get]
func (c *DashboardController) GetBudgetOverview(ctx *gin.Context) {
	// 获取当前用户部门ID
	departmentIDStr, exists := ctx.Get("department_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	// 检查是否指定了其他部门ID
	queryDeptID := ctx.Query("departmentId")
	var departmentID uuid.UUID
	var err error

	if queryDeptID != "" {
		departmentID, err = uuid.Parse(queryDeptID)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "部门ID格式错误",
			})
			return
		}
	} else {
		departmentID, err = uuid.Parse(departmentIDStr.(string))
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "部门ID格式错误",
			})
			return
		}
	}

	// 获取查询参数
	yearStr := ctx.DefaultQuery("year", strconv.Itoa(time.Now().Year()))
	year, err := strconv.Atoi(yearStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "年份参数格式错误",
		})
		return
	}

	// 获取预算概览
	budgetOverview, err := c.dashboardService.GetDepartmentBudgetOverview(departmentID, year)
	if err != nil {
		logger.Error("获取部门预算概览失败:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取部门预算概览失败: " + err.Error(),
		})
		return
	}

	// 标准RESTful API：直接返回数据，使用HTTP状态码表示结果
	ctx.JSON(http.StatusOK, budgetOverview)
}