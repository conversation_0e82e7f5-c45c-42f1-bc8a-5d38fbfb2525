package controllers

import (
	"net/http"
	"strconv"
	
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	"hospital-management/pkg/response"
)

type ExpenseController struct {
	expenseService *services.ExpenseService
	budgetService  *services.BudgetService
}

func NewExpenseController(expenseService *services.ExpenseService, budgetService *services.BudgetService) *ExpenseController {
	return &ExpenseController{
		expenseService: expenseService,
		budgetService:  budgetService,
	}
}

// CreateClaim 创建报销申请
// @Summary 创建报销申请
// @Description 提交一个新的通用报销申请
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param request body dto.CreateExpenseClaimRequest true "报销申请数据"
// @Success 201 {object} response.Response{data=dto.ExpenseClaimResponse} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/expense-claims [post]
func (c *ExpenseController) CreateClaim(ctx *gin.Context) {
	var req dto.CreateExpenseClaimRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("绑定JSON失败:", err)
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.Error(ctx, http.StatusUnauthorized, "用户ID格式错误")
		return
	}

	// 调用服务
	result, err := c.expenseService.CreateClaim(&req, userUUID)
	if err != nil {
		logger.Error("创建报销申请失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusCreated, "创建成功", result)
}

// GetClaim 获取报销申请详情
// @Summary 获取报销申请详情
// @Description 获取指定报销单的完整详情
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param id path string true "报销申请ID"
// @Success 200 {object} response.Response{data=dto.ExpenseClaimResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/expense-claims/{id} [get]
func (c *ExpenseController) GetClaim(ctx *gin.Context) {
	idStr := ctx.Param("id")
	claimID, err := uuid.Parse(idStr)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID格式")
		return
	}

	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	result, err := c.expenseService.GetClaim(claimID, userUUID)
	if err != nil {
		logger.Error("获取报销申请失败:", err)
		if err.Error() == "报销申请不存在" {
			response.Error(ctx, http.StatusNotFound, err.Error())
		} else {
			response.Error(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// GetClaimList 获取报销申请列表
// @Summary 获取报销申请列表
// @Description 获取当前用户的报销申请列表
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param status query string false "状态筛选"
// @Param keyword query string false "关键词搜索"
// @Param startDate query string false "开始日期"
// @Param endDate query string false "结束日期"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=dto.PagedResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/expense-claims [get]
func (c *ExpenseController) GetClaimList(ctx *gin.Context) {
	var req dto.GetExpenseClaimListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	result, err := c.expenseService.GetClaimList(&req, userUUID)
	if err != nil {
		logger.Error("获取报销申请列表失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// DeleteClaim 删除报销申请
// @Summary 删除报销申请
// @Description 删除指定的报销申请
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param id path string true "报销申请ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/expense-claims/{id} [delete]
func (c *ExpenseController) DeleteClaim(ctx *gin.Context) {
	idStr := ctx.Param("id")
	claimID, err := uuid.Parse(idStr)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID格式")
		return
	}

	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	err = c.expenseService.DeleteClaim(claimID, userUUID)
	if err != nil {
		logger.Error("删除报销申请失败:", err)
		if err.Error() == "报销申请不存在" {
			response.Error(ctx, http.StatusNotFound, err.Error())
		} else {
			response.Error(ctx, http.StatusBadRequest, err.Error())
		}
		return
	}

	response.Success(ctx, http.StatusOK, "删除成功", nil)
}

// WithdrawClaim 撤回报销申请
// @Summary 撤回报销申请
// @Description 撤回待审批的报销申请
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param id path string true "报销申请ID"
// @Success 200 {object} response.Response "撤回成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/expense-claims/{id}/withdraw [post]
func (c *ExpenseController) WithdrawClaim(ctx *gin.Context) {
	idStr := ctx.Param("id")
	claimID, err := uuid.Parse(idStr)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID格式")
		return
	}

	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	err = c.expenseService.WithdrawClaim(claimID, userUUID)
	if err != nil {
		logger.Error("撤回报销申请失败:", err)
		if err.Error() == "报销申请不存在" {
			response.Error(ctx, http.StatusNotFound, err.Error())
		} else {
			response.Error(ctx, http.StatusBadRequest, err.Error())
		}
		return
	}

	response.Success(ctx, http.StatusOK, "撤回成功", nil)
}

// GetBudgetBalance 获取预算余额
// @Summary 获取预算余额
// @Description 获取指定预算科目的余额信息
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param budgetItemId path string true "预算科目ID"
// @Success 200 {object} response.Response{data=dto.GetBudgetBalanceResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/budget-items/{budgetItemId}/balance [get]
func (c *ExpenseController) GetBudgetBalance(ctx *gin.Context) {
	var req dto.GetBudgetBalanceRequest
	if err := ctx.ShouldBindUri(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.budgetService.GetBudgetBalance(req.BudgetItemID)
	if err != nil {
		logger.Error("获取预算余额失败:", err)
		if err.Error() == "预算科目不存在" {
			response.Error(ctx, http.StatusNotFound, err.Error())
		} else {
			response.Error(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// GetExpenseTypes 获取费用类型列表
// @Summary 获取费用类型列表
// @Description 获取所有可用的费用类型
// @Tags 支出控制
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.ExpenseTypeResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/expense-types [get]
func (c *ExpenseController) GetExpenseTypes(ctx *gin.Context) {
	result, err := c.expenseService.GetExpenseTypes()
	if err != nil {
		logger.Error("获取费用类型失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// GetBudgetItems 获取预算科目列表
// @Summary 获取预算科目列表
// @Description 获取当前用户部门的预算科目列表
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param expenseTypeId query string false "费用类型ID筛选"
// @Success 200 {object} response.Response{data=[]dto.BudgetItemResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/budget-items [get]
func (c *ExpenseController) GetBudgetItems(ctx *gin.Context) {
	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	expenseTypeIDStr := ctx.Query("expenseTypeId")
	var expenseTypeID *uuid.UUID
	if expenseTypeIDStr != "" {
		id, err := uuid.Parse(expenseTypeIDStr)
		if err != nil {
			response.Error(ctx, http.StatusBadRequest, "无效的费用类型ID格式")
			return
		}
		expenseTypeID = &id
	}

	result, err := c.budgetService.GetBudgetItemsByUser(userUUID, expenseTypeID)
	if err != nil {
		logger.Error("获取预算科目失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "获取成功", result)
}

// SearchPreApprovals 搜索事前申请
// @Summary 搜索事前申请
// @Description 根据关键词搜索事前申请单
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Param limit query int false "返回数量限制" default(10)
// @Success 200 {object} response.Response{data=[]dto.PreApprovalSearchResult} "搜索成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/pre-approvals/search [get]
func (c *ExpenseController) SearchPreApprovals(ctx *gin.Context) {
	var req dto.SearchPreApprovalsRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	result, err := c.expenseService.SearchPreApprovals(req.Keyword, userUUID, req.Limit)
	if err != nil {
		logger.Error("搜索事前申请失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "搜索成功", result)
}

// SearchContracts 搜索合同
// @Summary 搜索合同
// @Description 根据关键词搜索合同
// @Tags 支出控制
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Param limit query int false "返回数量限制" default(10)
// @Success 200 {object} response.Response{data=[]dto.ContractSearchResult} "搜索成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/contracts/search [get]
func (c *ExpenseController) SearchContracts(ctx *gin.Context) {
	var req dto.SearchContractsRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	userID, _ := ctx.Get("userID")
	userUUID := userID.(uuid.UUID)

	result, err := c.expenseService.SearchContracts(req.Keyword, userUUID, req.Limit)
	if err != nil {
		logger.Error("搜索合同失败:", err)
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, http.StatusOK, "搜索成功", result)
}
