package database

import (
	"fmt"
	"hospital-management/internal/config"
	"hospital-management/pkg/logger"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// Connect 连接到数据库
func Connect(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.DBName, cfg.SSLMode,
	)

	logger.Info("正在连接数据库...")

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		Logger: NewGormLogger(),
	})

	if err != nil {
		logger.Error("数据库连接失败:", err)
		return nil, err
	}

	// 设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		logger.Error("获取数据库连接池失败:", err)
		return nil, err
	}

	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(10)
	// 设置最大打开连接数
	sqlDB.SetMaxOpenConns(100)
	// 设置连接最大生存时间
	sqlDB.SetConnMaxLifetime(time.Hour)

	logger.Info("数据库连接成功")
	return db, nil
}