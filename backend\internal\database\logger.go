package database

import (
	"context"
	"fmt"
	"hospital-management/pkg/logger"
	"time"

	"gorm.io/gorm/utils"

	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

// GormLogger 自定义GORM日志记录器
type GormLogger struct {
	SlowThreshold         time.Duration
	SourceField           string
	SkipErrRecordNotFound bool
}

// NewGormLogger 创建一个新的GORM日志记录器
func NewGormLogger() *GormLogger {
	return &GormLogger{
		SlowThreshold:         time.Second, // 慢查询阈值
		SkipErrRecordNotFound: true,        // 跳过记录未找到错误
	}
}

// LogMode 设置日志级别
func (l *GormLogger) LogMode(level gormlogger.LogLevel) gormlogger.Interface {
	return l
}

// Info 记录信息日志
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	logger.Infof(msg, data...)
}

// Warn 记录警告日志
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	logger.Warnf(msg, data...)
}

// Error 记录错误日志
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	logger.Errorf(msg, data...)
}

// Trace 记录SQL查询日志
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	
	// 获取调用者信息
	source := utils.FileWithLineNum()
	
	// 构建日志消息
	logMsg := fmt.Sprintf("[%.3fms] [rows:%v] %s", float64(elapsed.Nanoseconds())/1e6, rows, sql)
	
	// 根据不同情况记录日志
	switch {
	case err != nil && (!l.SkipErrRecordNotFound || err != gorm.ErrRecordNotFound):
		// 记录错误
		logger.Errorf("%s %s\n[错误] %v", source, logMsg, err)
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0:
		// 记录慢查询
		logger.Warnf("%s %s\n[慢查询] 超过 %v", source, logMsg, l.SlowThreshold)
	default:
		// 记录正常查询
		logger.Debugf("%s %s", source, logMsg)
	}
}