-- 费用类型表
CREATE TABLE IF NOT EXISTS expense_types (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 事前申请表（简化版）
CREATE TABLE IF NOT EXISTS pre_approvals (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    applicant_id BIGINT NOT NULL REFERENCES users(id),
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 合同表（简化版）
CREATE TABLE IF NOT EXISTS contracts (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    contract_no VARCHAR(100) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    signed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 报销单主表
CREATE TABLE IF NOT EXISTS expense_claims (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    applicant_id BIGINT NOT NULL REFERENCES users(id),
    department_id BIGINT NOT NULL REFERENCES departments(id),
    total_amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    payee_type VARCHAR(20) NOT NULL,
    payee_info JSONB,
    workflow_instance_id VARCHAR(50),
    related_pre_approval_id BIGINT REFERENCES pre_approvals(id),
    related_contract_id BIGINT REFERENCES contracts(id),
    submitted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 报销单明细表
CREATE TABLE IF NOT EXISTS expense_claim_details (
    id BIGSERIAL PRIMARY KEY,
    claim_id BIGINT NOT NULL REFERENCES expense_claims(id) ON DELETE CASCADE,
    expense_type_id BIGINT NOT NULL REFERENCES expense_types(id),
    budget_item_id BIGINT NOT NULL REFERENCES budget_items(id),
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    invoice_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 报销单附件表
CREATE TABLE IF NOT EXISTS expense_claim_attachments (
    id BIGSERIAL PRIMARY KEY,
    claim_id BIGINT NOT NULL REFERENCES expense_claims(id) ON DELETE CASCADE,
    file_id VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 预算冻结记录表
CREATE TABLE IF NOT EXISTS budget_freezes (
    id BIGSERIAL PRIMARY KEY,
    budget_item_id BIGINT NOT NULL REFERENCES budget_items(id),
    amount DECIMAL(15,2) NOT NULL,
    business_id BIGINT NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 工作流实例表
CREATE TABLE IF NOT EXISTS workflow_instances (
    id BIGSERIAL PRIMARY KEY,
    definition_key VARCHAR(100) NOT NULL,
    business_id BIGINT NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    current_node_id VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    start_user_id BIGINT NOT NULL REFERENCES users(id),
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    variables JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 审批历史记录表
CREATE TABLE IF NOT EXISTS workflow_approval_history (
    id BIGSERIAL PRIMARY KEY,
    instance_id BIGINT NOT NULL REFERENCES workflow_instances(id),
    node_id VARCHAR(100) NOT NULL,
    node_name VARCHAR(100) NOT NULL,
    approver_id BIGINT NOT NULL REFERENCES users(id),
    action VARCHAR(20) NOT NULL,
    comment TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 工作流定义表
CREATE TABLE IF NOT EXISTS workflow_definitions (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    definition JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_expense_claims_applicant_id ON expense_claims(applicant_id);
CREATE INDEX IF NOT EXISTS idx_expense_claims_status ON expense_claims(status);
CREATE INDEX IF NOT EXISTS idx_expense_claims_submitted_at ON expense_claims(submitted_at);
CREATE INDEX IF NOT EXISTS idx_expense_claim_details_claim_id ON expense_claim_details(claim_id);
CREATE INDEX IF NOT EXISTS idx_budget_freezes_budget_item_id ON budget_freezes(budget_item_id);
CREATE INDEX IF NOT EXISTS idx_budget_freezes_business ON budget_freezes(business_id, business_type);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_business ON workflow_instances(business_id, business_type);
CREATE INDEX IF NOT EXISTS idx_workflow_approval_history_instance_id ON workflow_approval_history(instance_id);

-- 插入初始费用类型数据
INSERT INTO expense_types (code, name, description) VALUES
('OFFICE', '办公费', '办公用品、设备等费用'),
('ENTERTAINMENT', '招待费', '业务招待相关费用'),
('TRAVEL', '差旅费', '出差交通、住宿等费用'),
('TRAINING', '培训费', '员工培训相关费用'),
('MAINTENANCE', '维修费', '设备维修保养费用'),
('COMMUNICATION', '通讯费', '电话、网络等通讯费用')
ON CONFLICT (code) DO NOTHING;

-- 插入示例事前申请数据
INSERT INTO pre_approvals (title, applicant_id, amount, status) VALUES
('购买办公设备申请', 1, 5000.00, 'approved'),
('培训费用申请', 1, 3000.00, 'approved'),
('差旅费用申请', 1, 2000.00, 'pending')
ON CONFLICT DO NOTHING;

-- 插入示例合同数据
INSERT INTO contracts (name, contract_no, amount, status, signed_at) VALUES
('办公用品采购合同', 'HT2024001', 50000.00, 'active', NOW()),
('设备维护服务合同', 'HT2024002', 30000.00, 'active', NOW()),
('培训服务合同', 'HT2024003', 20000.00, 'active', NOW())
ON CONFLICT (contract_no) DO NOTHING;