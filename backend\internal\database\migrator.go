package database

import (
	"fmt"
	"log"
	"time"

	"hospital-management/internal/models"

	"gorm.io/gorm"
)

// Migra<PERSON> 高级迁移管理器
type Migrator struct {
	db *gorm.DB
}

// NewMigrator 创建迁移器
func NewMigrator(db *gorm.DB) *Migrator {
	return &Migrator{db: db}
}

// InitMigrationTable 初始化迁移记录表
func (m *Migrator) InitMigrationTable() error {
	return m.db.AutoMigrate(&models.Migration{})
}

// RecordMigration 记录迁移
func (m *Migrator) RecordMigration(version, name, description string) error {
	migration := models.Migration{
		Version:     version,
		Name:        name,
		Description: description,
		Status:      "completed",
		StartedAt:   timePtr(time.Now()),
		CompletedAt: timePtr(time.Now()),
	}

	return m.db.Create(&migration).Error
}

// IsMigrationCompleted 检查迁移是否已完成
func (m *Migrator) IsMigrationCompleted(version string) (bool, error) {
	var count int64
	err := m.db.Model(&models.Migration{}).
		Where("version = ? AND status = ?", version, "completed").
		Count(&count).Error

	return count > 0, err
}

// GetMigrationHistory 获取迁移历史
func (m *Migrator) GetMigrationHistory() ([]models.Migration, error) {
	var migrations []models.Migration
	err := m.db.Order("created_at DESC").Find(&migrations).Error
	return migrations, err
}

// RunInitialMigration 运行初始迁移
func (m *Migrator) RunInitialMigration() error {
	// 初始化迁移表
	if err := m.InitMigrationTable(); err != nil {
		return fmt.Errorf("初始化迁移表失败: %w", err)
	}

	// 检查是否已经运行过初始迁移
	completed, err := m.IsMigrationCompleted("v1.0.0")
	if err != nil {
		return fmt.Errorf("检查迁移状态失败: %w", err)
	}

	// 额外检查：验证关键表是否存在且有数据
	if completed {
		hasData, err := m.verifyInitialData()
		if err != nil {
			log.Printf("验证初始数据失败: %v", err)
		} else if hasData {
			log.Println("初始迁移已完成且数据完整，跳过")
			return nil
		} else {
			log.Println("迁移记录存在但数据不完整，重新运行迁移...")
		}
	}

	log.Println("开始运行初始迁移...")

	// 运行迁移
	migrationManager := NewMigrationManager(m.db)
	if err := migrationManager.RunMigrations(); err != nil {
		return fmt.Errorf("运行迁移失败: %w", err)
	}

	// 记录迁移完成
	if err := m.RecordMigration("v1.0.0", "初始迁移", "创建所有基础表和初始数据"); err != nil {
		return fmt.Errorf("记录迁移失败: %w", err)
	}

	log.Println("初始迁移完成")
	return nil
}

// verifyInitialData 验证初始数据是否存在
func (m *Migrator) verifyInitialData() (bool, error) {
	// 检查用户表是否有数据
	var userCount int64
	if err := m.db.Model(&models.User{}).Count(&userCount).Error; err != nil {
		return false, err
	}

	// 检查部门表是否有数据
	var deptCount int64
	if err := m.db.Model(&models.Department{}).Count(&deptCount).Error; err != nil {
		return false, err
	}

	// 如果用户和部门都有数据，认为初始化完成
	return userCount > 0 && deptCount > 0, nil
}

// 辅助函数
func timePtr(t time.Time) *time.Time {
	return &t
}