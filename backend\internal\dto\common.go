package dto

// ApiResponse 标准API响应格式
type ApiResponse[T any] struct {
	Code    int    `json:"code"`    // 状态码，200表示成功，其他为标准HTTP状态码
	Message string `json:"message"` // 响应消息
	Data    T      `json:"data"`    // 响应数据
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse[T any](data T) ApiResponse[T] {
	return ApiResponse[T]{
		Code:    200,
		Message: "success",
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse[T any](code int, message string) ApiResponse[T] {
	return ApiResponse[T]{
		Code:    code,
		Message: message,
	}
}
