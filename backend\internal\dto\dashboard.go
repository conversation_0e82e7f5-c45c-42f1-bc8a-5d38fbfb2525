package dto

import (
	"time"

	"github.com/google/uuid"
)

// BudgetOverviewResponse 预算概览响应
type BudgetOverviewResponse struct {
	TotalBudget     float64 `json:"totalBudget"`     // 部门年度总预算
	UsedBudget      float64 `json:"usedBudget"`      // 已使用金额
	FrozenBudget    float64 `json:"frozenBudget"`    // 冻结金额
	AvailableBudget float64 `json:"availableBudget"` // 可用余额
}

// ApprovalItemResponse 审批项响应
type ApprovalItemResponse struct {
	Type           string    `json:"type"`           // 审批类型，如"报销申请"、"合同审批"
	Applicant      string    `json:"applicant"`      // 申请人
	Summary        string    `json:"summary"`        // 摘要
	Amount         float64   `json:"amount,omitempty"` // 涉及金额（可选）
	SubmissionTime time.Time `json:"submissionTime"` // 提交时间
	Link           string    `json:"link"`           // 跳转到审批页面的路由
}

// DashboardOverviewResponse 工作台概览响应
type DashboardOverviewResponse struct {
	BudgetOverview   BudgetOverviewResponse `json:"budgetOverview"`   // 预算概览
	PendingApprovals []ApprovalItemResponse `json:"pendingApprovals"` // 待审批列表
}

// DashboardRequest 工作台请求
type DashboardRequest struct {
	DepartmentID uuid.UUID `form:"departmentId"` // 部门ID（可选，默认为当前用户部门）
	Year         int       `form:"year"`         // 年份（可选，默认为当前年份）
}