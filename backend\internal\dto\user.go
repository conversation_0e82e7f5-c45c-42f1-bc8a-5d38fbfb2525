package dto

import (
	"github.com/google/uuid"
	"time"
)

// UserResponse 用户响应
type UserResponse struct {
	ID            uuid.UUID  `json:"id"`
	Username      string     `json:"username"`
	FullName      string     `json:"fullName"`
	EmployeeID    string     `json:"employeeId"`
	DepartmentID  uuid.UUID  `json:"departmentId"`
	DepartmentName string    `json:"departmentName"`
	Title         string     `json:"title"`
	Roles         []string   `json:"roles"`
	Avatar        string     `json:"avatar,omitempty"`
	Email         string     `json:"email,omitempty"`
	Phone         string     `json:"phone,omitempty"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     time.Time  `json:"updatedAt"`
}

// DepartmentSimpleResponse 简单部门响应
type DepartmentSimpleResponse struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
	Code string    `json:"code"`
}

// RoleSimpleResponse 简单角色响应
type RoleSimpleResponse struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
	Code string    `json:"code"`
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page     int    `form:"page" binding:"required,min=1"`
	PageSize int    `form:"pageSize" binding:"required,min=1,max=100"`
	Keyword  string `form:"keyword"`
	Status   string `form:"status"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Total int64         `json:"total"`
	Items []UserResponse `json:"items"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username     string    `json:"username" binding:"required,min=3,max=50"`
	Password     string    `json:"password" binding:"required,min=6,max=50"`
	FullName     string    `json:"fullName" binding:"required,max=100"`
	EmployeeID   string    `json:"employeeId" binding:"required,max=50"`
	DepartmentID uuid.UUID `json:"departmentId" binding:"required"`
	Title        string    `json:"title" binding:"max=50"`
	Roles        []string  `json:"roles" binding:"required"`
	Email        string    `json:"email" binding:"omitempty,email,max=100"`
	Phone        string    `json:"phone" binding:"omitempty,max=20"`
	Avatar       string    `json:"avatar" binding:"omitempty,max=255"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	FullName     string    `json:"fullName" binding:"omitempty,max=100"`
	DepartmentID uuid.UUID `json:"departmentId" binding:"omitempty"`
	Title        string    `json:"title" binding:"omitempty,max=50"`
	Roles        []string  `json:"roles" binding:"omitempty"`
	Email        string    `json:"email" binding:"omitempty,email,max=100"`
	Phone        string    `json:"phone" binding:"omitempty,max=20"`
	Avatar       string    `json:"avatar" binding:"omitempty,max=255"`
	Status       string    `json:"status" binding:"omitempty,oneof=active inactive"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required,min=6,max=50"`
	NewPassword string `json:"newPassword" binding:"required,min=6,max=50"`
}