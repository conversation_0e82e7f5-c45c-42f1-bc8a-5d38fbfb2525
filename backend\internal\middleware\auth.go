package middleware

import (
	"hospital-management/pkg/jwt"
	"net/http"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth(secretKey string) gin.HandlerFunc {
	jwtManager := jwt.NewJWTManager(secretKey)

	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "未提供认证令牌",
			})
			c.Abort()
			return
		}

		// 提取令牌
		tokenString := jwt.ExtractTokenFromHeader(authHeader)
		if tokenString == "" {
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效的认证令牌格式",
			})
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "认证令牌无效",
				"error":   err.<PERSON>rror(),
			})
			c.Abort()
			return
		}

		// 检查令牌类型（只允许访问令牌）
		if claims.TokenType != "access" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效的令牌类型",
			})
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set("user_id", claims.UserID)
		c.Set("user_name", claims.UserName)
		c.Set("department_id", claims.DepartmentID)
		c.Set("roles", claims.Roles)
		c.Set("permissions", claims.Permissions)

		c.Next()
	}
}
