package models

import (
	"github.com/google/uuid"
)

// BudgetItem 预算项表
type BudgetItem struct {
	BaseModel
	Year         int       `json:"year" gorm:"type:int;not null"`
	DepartmentID uuid.UUID `json:"department_id" gorm:"type:uuid;not null"`
	Department   *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	SubjectCode  string    `json:"subject_code" gorm:"type:varchar(50);not null"`
	SubjectName  string    `json:"subject_name" gorm:"type:varchar(100);not null"`
	TotalAmount  float64   `json:"total_amount" gorm:"type:decimal(18,2);not null"`
	UsedAmount   float64   `json:"used_amount" gorm:"type:decimal(18,2);default:0"`
	FrozenAmount float64   `json:"frozen_amount" gorm:"type:decimal(18,2);default:0"`
	ControlType  string    `json:"control_type" gorm:"type:varchar(20);default:'rigid'"` // rigid: 刚性控制, flexible: 弹性控制
	Status       string    `json:"status" gorm:"type:varchar(20);default:'active'"`
}

// TableName 指定表名
func (BudgetItem) TableName() string {
	return "budget_items"
}

// BudgetFreeze 预算冻结记录表
type BudgetFreeze struct {
	BaseModel
	BudgetItemID uuid.UUID   `json:"budget_item_id" gorm:"type:uuid;not null"`
	BudgetItem   *BudgetItem `json:"budget_item,omitempty" gorm:"foreignKey:BudgetItemID"`
	Amount       float64     `json:"amount" gorm:"type:decimal(18,2);not null"`
	BusinessID   uuid.UUID   `json:"business_id" gorm:"type:uuid;not null"`
	BusinessType string      `json:"business_type" gorm:"type:varchar(50);not null"` // pre_approval, contract, expense_claim
	Status       string      `json:"status" gorm:"type:varchar(20);default:'active'"` // active, released, used
}

// TableName 指定表名
func (BudgetFreeze) TableName() string {
	return "budget_freezes"
}