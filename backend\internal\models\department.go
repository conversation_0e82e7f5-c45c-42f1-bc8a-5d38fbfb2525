package models

import (
	"github.com/google/uuid"
)

// Department 部门信息表
type Department struct {
	BaseModel
	Name     string     `json:"name" gorm:"type:varchar(100);not null"`
	Code     string     `json:"code" gorm:"type:varchar(50);uniqueIndex;not null"`
	ParentID *uuid.UUID `json:"parent_id,omitempty" gorm:"type:uuid"`
	Parent   *Department `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Level    int        `json:"level" gorm:"type:int;default:1"`
	Sort     int        `json:"sort" gorm:"type:int;default:0"`
	Status   string     `json:"status" gorm:"type:varchar(20);default:'active'"`
}

// TableName 指定表名
func (Department) TableName() string {
	return "departments"
}