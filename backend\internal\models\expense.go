package models

import (
	"github.com/google/uuid"
	"time"
	"encoding/json"
)

// ExpenseClaim 报销单主表
type ExpenseClaim struct {
	BaseModel
	Title                string          `json:"title" gorm:"type:varchar(200);not null"`
	ApplicantID          uuid.UUID       `json:"applicant_id" gorm:"type:uuid;not null"`
	Applicant            *User           `json:"applicant,omitempty" gorm:"foreignKey:ApplicantID"`
	DepartmentID         uuid.UUID       `json:"department_id" gorm:"type:uuid;not null"`
	Department           *Department     `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	TotalAmount          float64         `json:"total_amount" gorm:"type:decimal(18,2);not null"`
	Status               string          `json:"status" gorm:"type:varchar(20);not null;default:'draft'"` // draft, pending, approved, rejected, paid
	WorkflowInstanceID   *uuid.UUID      `json:"workflow_instance_id,omitempty" gorm:"type:uuid"`
	WorkflowInstance     *WorkflowInstance `json:"workflow_instance,omitempty" gorm:"foreignKey:WorkflowInstanceID"`
	RelatedPreApprovalID *uuid.UUID      `json:"related_pre_approval_id,omitempty" gorm:"type:uuid"`
	RelatedContractID    *uuid.UUID      `json:"related_contract_id,omitempty" gorm:"type:uuid"`
	SubmittedAt          *time.Time      `json:"submitted_at,omitempty"`
	PayeeType            string          `json:"payee_type" gorm:"type:varchar(20);not null"` // personal, corporate
	PayeeInfo            json.RawMessage `json:"payee_info" gorm:"type:jsonb"`
	Details              []ExpenseClaimDetail `json:"details,omitempty" gorm:"foreignKey:ClaimID"`
	Attachments          json.RawMessage `json:"attachments,omitempty" gorm:"type:jsonb"`
}

// TableName 指定表名
func (ExpenseClaim) TableName() string {
	return "expense_claims"
}

// ExpenseClaimDetail 报销单明细表
type ExpenseClaimDetail struct {
	BaseModel
	ClaimID       uuid.UUID   `json:"claim_id" gorm:"type:uuid;not null"`
	Claim         *ExpenseClaim `json:"claim,omitempty" gorm:"foreignKey:ClaimID"`
	ExpenseTypeID uuid.UUID   `json:"expense_type_id" gorm:"type:uuid;not null"`
	ExpenseType   *ExpenseType `json:"expense_type,omitempty" gorm:"foreignKey:ExpenseTypeID"`
	BudgetItemID  uuid.UUID   `json:"budget_item_id" gorm:"type:uuid;not null"`
	BudgetItem    *BudgetItem `json:"budget_item,omitempty" gorm:"foreignKey:BudgetItemID"`
	Amount        float64     `json:"amount" gorm:"type:decimal(18,2);not null"`
	Description   string      `json:"description" gorm:"type:text"`
	InvoiceData   json.RawMessage `json:"invoice_data,omitempty" gorm:"type:jsonb"`
}

// TableName 指定表名
func (ExpenseClaimDetail) TableName() string {
	return "expense_claim_details"
}

// ExpenseType 费用类型表
type ExpenseType struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(100);not null"`
	Code        string `json:"code" gorm:"type:varchar(50);uniqueIndex;not null"`
	Description string `json:"description" gorm:"type:text"`
	Status      string `json:"status" gorm:"type:varchar(20);default:'active'"`
}

// TableName 指定表名
func (ExpenseType) TableName() string {
	return "expense_types"
}