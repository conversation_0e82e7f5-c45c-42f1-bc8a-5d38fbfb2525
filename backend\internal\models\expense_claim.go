package models

import (
	"time"
	"gorm.io/gorm"
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// ExpenseClaim 报销单主表
type ExpenseClaim struct {
	ID                    uint                   `json:"id" gorm:"primaryKey"`
	Title                 string                 `json:"title" gorm:"not null;comment:报销事由"`
	ApplicantID           uint                   `json:"applicant_id" gorm:"not null;comment:申请人ID"`
	Applicant             User                   `json:"applicant" gorm:"foreignKey:ApplicantID"`
	DepartmentID          uint                   `json:"department_id" gorm:"not null;comment:部门ID"`
	Department            Department             `json:"department" gorm:"foreignKey:DepartmentID"`
	TotalAmount           float64                `json:"total_amount" gorm:"type:decimal(15,2);not null;comment:报销总金额"`
	Status                string                 `json:"status" gorm:"default:draft;comment:状态(draft,pending,approved,rejected,paid)"`
	PayeeType             string                 `json:"payee_type" gorm:"comment:收款人类型(personal,corporate)"`
	PayeeInfo             PayeeInfo              `json:"payee_info" gorm:"type:jsonb;comment:收款人信息"`
	WorkflowInstanceID    *string                `json:"workflow_instance_id" gorm:"comment:工作流实例ID"`
	RelatedPreApprovalID  *uint                  `json:"related_pre_approval_id" gorm:"comment:关联事前申请ID"`
	RelatedPreApproval    *PreApproval           `json:"related_pre_approval,omitempty" gorm:"foreignKey:RelatedPreApprovalID"`
	RelatedContractID     *uint                  `json:"related_contract_id" gorm:"comment:关联合同ID"`
	RelatedContract       *Contract              `json:"related_contract,omitempty" gorm:"foreignKey:RelatedContractID"`
	SubmittedAt           *time.Time             `json:"submitted_at" gorm:"comment:提交时间"`
	ApprovedAt            *time.Time             `json:"approved_at" gorm:"comment:审批通过时间"`
	PaidAt                *time.Time             `json:"paid_at" gorm:"comment:支付时间"`
	Details               []ExpenseClaimDetail   `json:"details" gorm:"foreignKey:ClaimID"`
	Attachments           []ExpenseClaimAttachment `json:"attachments" gorm:"foreignKey:ClaimID"`
	CreatedAt             time.Time              `json:"created_at"`
	UpdatedAt             time.Time              `json:"updated_at"`
	DeletedAt             gorm.DeletedAt         `json:"deleted_at" gorm:"index"`
}

// ExpenseClaimDetail 报销单明细表
type ExpenseClaimDetail struct {
	ID              uint        `json:"id" gorm:"primaryKey"`
	ClaimID         uint        `json:"claim_id" gorm:"not null;comment:报销单ID"`
	ExpenseTypeID   uint        `json:"expense_type_id" gorm:"not null;comment:费用类型ID"`
	ExpenseType     ExpenseType `json:"expense_type" gorm:"foreignKey:ExpenseTypeID"`
	BudgetItemID    uint        `json:"budget_item_id" gorm:"not null;comment:预算科目ID"`
	BudgetItem      BudgetItem  `json:"budget_item" gorm:"foreignKey:BudgetItemID"`
	Amount          float64     `json:"amount" gorm:"type:decimal(15,2);not null;comment:报销金额"`
	Description     string      `json:"description" gorm:"comment:费用说明"`
	InvoiceData     InvoiceInfo `json:"invoice_data" gorm:"type:jsonb;comment:发票信息"`
	CreatedAt       time.Time   `json:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at"`
}

// ExpenseClaimAttachment 报销单附件表
type ExpenseClaimAttachment struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	ClaimID      uint      `json:"claim_id" gorm:"not null;comment:报销单ID"`
	FileName     string    `json:"file_name" gorm:"not null;comment:文件名"`
	OriginalName string    `json:"original_name" gorm:"not null;comment:原始文件名"`
	FilePath     string    `json:"file_path" gorm:"not null;comment:文件路径"`
	FileSize     int64     `json:"file_size" gorm:"comment:文件大小"`
	FileType     string    `json:"file_type" gorm:"comment:文件类型"`
	UploadedBy   uint      `json:"uploaded_by" gorm:"comment:上传人ID"`
	CreatedAt    time.Time `json:"created_at"`
}

// ExpenseType 费用类型表
type ExpenseType struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Code        string    `json:"code" gorm:"uniqueIndex;not null;comment:费用类型编码"`
	Name        string    `json:"name" gorm:"not null;comment:费用类型名称"`
	Description string    `json:"description" gorm:"comment:描述"`
	IsActive    bool      `json:"is_active" gorm:"default:true;comment:是否启用"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// PayeeInfo 收款人信息
type PayeeInfo struct {
	Type        string `json:"type"`         // personal, corporate
	Name        string `json:"name"`         // 收款人姓名/公司名称
	BankName    string `json:"bank_name"`    // 开户银行
	AccountNo   string `json:"account_no"`   // 账号
	TaxNo       string `json:"tax_no"`       // 税号(对公)
	Address     string `json:"address"`      // 地址(对公)
	Phone       string `json:"phone"`        // 电话(对公)
}

// InvoiceInfo 发票信息
type InvoiceInfo struct {
	Code       string    `json:"code"`        // 发票代码
	Number     string    `json:"number"`      // 发票号码
	Date       time.Time `json:"date"`        // 开票日期
	Amount     float64   `json:"amount"`      // 发票金额
	SellerName string    `json:"seller_name"` // 销售方名称
	SellerTaxNo string   `json:"seller_tax_no"` // 销售方税号
	BuyerName  string    `json:"buyer_name"`  // 购买方名称
	BuyerTaxNo string    `json:"buyer_tax_no"` // 购买方税号
	Verified   bool      `json:"verified"`    // 是否已验证
}

// PreApproval 事前申请表（简化版，用于关联）
type PreApproval struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Title       string    `json:"title" gorm:"not null;comment:申请事由"`
	Amount      float64   `json:"amount" gorm:"type:decimal(15,2);comment:申请金额"`
	ApplicantID uint      `json:"applicant_id" gorm:"not null;comment:申请人ID"`
	Status      string    `json:"status" gorm:"default:draft;comment:状态"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Contract 合同表（简化版，用于关联）
type Contract struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	Name       string    `json:"name" gorm:"not null;comment:合同名称"`
	ContractNo string    `json:"contract_no" gorm:"not null;comment:合同编号"`
	Amount     float64   `json:"amount" gorm:"type:decimal(15,2);comment:合同金额"`
	Status     string    `json:"status" gorm:"default:draft;comment:状态"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// GORM自定义类型实现
func (p PayeeInfo) Value() (driver.Value, error) {
	return json.Marshal(p)
}

func (p *PayeeInfo) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, p)
}

func (i InvoiceInfo) Value() (driver.Value, error) {
	return json.Marshal(i)
}

func (i *InvoiceInfo) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, i)
}

// TableName 指定表名
func (ExpenseClaim) TableName() string {
	return "expense_claims"
}

func (ExpenseClaimDetail) TableName() string {
	return "expense_claim_details"
}

func (ExpenseClaimAttachment) TableName() string {
	return "expense_claim_attachments"
}

func (ExpenseType) TableName() string {
	return "expense_types"
}

func (PreApproval) TableName() string {
	return "pre_approvals"
}

func (Contract) TableName() string {
	return "contracts"
}

// 状态常量
const (
	ExpenseClaimStatusDraft    = "draft"
	ExpenseClaimStatusPending  = "pending"
	ExpenseClaimStatusApproved = "approved"
	ExpenseClaimStatusRejected = "rejected"
	ExpenseClaimStatusPaid     = "paid"
)

// 收款人类型常量
const (
	PayeeTypePersonal   = "personal"
	PayeeTypeCorporate  = "corporate"
)

// 业务方法
func (ec *ExpenseClaim) CanEdit() bool {
	return ec.Status == ExpenseClaimStatusDraft || ec.Status == ExpenseClaimStatusRejected
}

func (ec *ExpenseClaim) CanDelete() bool {
	return ec.Status == ExpenseClaimStatusDraft
}

func (ec *ExpenseClaim) CanWithdraw() bool {
	return ec.Status == ExpenseClaimStatusPending
}

func (ec *ExpenseClaim) CanApprove() bool {
	return ec.Status == ExpenseClaimStatusPending
}

// 计算总金额
func (ec *ExpenseClaim) CalculateTotalAmount() {
	total := 0.0
	for _, detail := range ec.Details {
		total += detail.Amount
	}
	ec.TotalAmount = total
}
