package models

import (
	"time"
)

// Migration 数据库迁移记录表
type Migration struct {
	ID          int       `json:"id" gorm:"primaryKey;autoIncrement"`
	Version     string    `json:"version" gorm:"type:varchar(50);not null;unique"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Status      string    `json:"status" gorm:"type:varchar(20);not null;default:'pending'"` // pending, running, completed, failed
	StartedAt   *time.Time `json:"started_at,omitempty" gorm:"type:timestamptz"`
	CompletedAt *time.Time `json:"completed_at,omitempty" gorm:"type:timestamptz"`
	ErrorMsg    *string   `json:"error_msg,omitempty" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (Migration) TableName() string {
	return "tbl_migrations"
}
