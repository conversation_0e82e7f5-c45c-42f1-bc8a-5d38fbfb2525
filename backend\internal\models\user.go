package models

import (
	"github.com/google/uuid"
	"github.com/lib/pq"
)

// User 用户信息表
type User struct {
	BaseModel
	Username     string         `json:"username" gorm:"type:varchar(50);uniqueIndex;not null"`
	PasswordHash string         `json:"-" gorm:"type:varchar(255);not null"`
	FullName     string         `json:"full_name" gorm:"type:varchar(100);not null"`
	DepartmentID uuid.UUID      `json:"department_id" gorm:"type:uuid;not null"`
	Department   *Department    `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	EmployeeID   string         `json:"employee_id" gorm:"type:varchar(50);uniqueIndex;not null"`
	Title        string         `json:"title" gorm:"type:varchar(50)"`
	Roles        pq.StringArray `json:"roles" gorm:"type:text[]"`
	Avatar       string         `json:"avatar" gorm:"type:varchar(255)"`
	Email        string         `json:"email" gorm:"type:varchar(100)"`
	Phone        string         `json:"phone" gorm:"type:varchar(20)"`
	Status       string         `json:"status" gorm:"type:varchar(20);default:'active'"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}