package models

import (
	"github.com/google/uuid"
	"time"
)

// WorkflowInstance 流程实例表
type WorkflowInstance struct {
	BaseModel
	DefinitionKey string     `json:"definition_key" gorm:"type:varchar(100);not null"`
	BusinessID    uuid.UUID  `json:"business_id" gorm:"type:uuid;not null"`
	BusinessType  string     `json:"business_type" gorm:"type:varchar(50);not null"` // expense_claim, contract, etc.
	CurrentNodeID string     `json:"current_node_id" gorm:"type:varchar(100);not null"`
	Status        string     `json:"status" gorm:"type:varchar(20);not null;default:'active'"` // active, completed, terminated
	StartUserID   uuid.UUID  `json:"start_user_id" gorm:"type:uuid;not null"`
	StartUser     *User      `json:"start_user,omitempty" gorm:"foreignKey:StartUserID"`
	StartTime     time.Time  `json:"start_time" gorm:"not null"`
	EndTime       *time.Time `json:"end_time,omitempty"`
	History       []WorkflowApprovalHistory `json:"history,omitempty" gorm:"foreignKey:InstanceID"`
}

// TableName 指定表名
func (WorkflowInstance) TableName() string {
	return "workflow_instances"
}

// WorkflowApprovalHistory 审批历史记录表
type WorkflowApprovalHistory struct {
	BaseModel
	InstanceID  uuid.UUID        `json:"instance_id" gorm:"type:uuid;not null"`
	Instance    *WorkflowInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID"`
	NodeID      string           `json:"node_id" gorm:"type:varchar(100);not null"`
	NodeName    string           `json:"node_name" gorm:"type:varchar(100);not null"`
	ApproverID  uuid.UUID        `json:"approver_id" gorm:"type:uuid;not null"`
	Approver    *User            `json:"approver,omitempty" gorm:"foreignKey:ApproverID"`
	Action      string           `json:"action" gorm:"type:varchar(20);not null"` // approve, reject
	Comment     string           `json:"comment" gorm:"type:text"`
}

// TableName 指定表名
func (WorkflowApprovalHistory) TableName() string {
	return "workflow_approval_history"
}

// WorkflowDefinition 流程定义表
type WorkflowDefinition struct {
	BaseModel
	Key         string `json:"key" gorm:"type:varchar(100);uniqueIndex;not null"`
	Name        string `json:"name" gorm:"type:varchar(100);not null"`
	Description string `json:"description" gorm:"type:text"`
	Version     int    `json:"version" gorm:"type:int;not null;default:1"`
	Status      string `json:"status" gorm:"type:varchar(20);not null;default:'active'"` // active, inactive
	Nodes       []WorkflowNode `json:"nodes,omitempty" gorm:"foreignKey:DefinitionID"`
}

// TableName 指定表名
func (WorkflowDefinition) TableName() string {
	return "workflow_definitions"
}

// WorkflowNode 流程节点表
type WorkflowNode struct {
	BaseModel
	DefinitionID uuid.UUID          `json:"definition_id" gorm:"type:uuid;not null"`
	Definition   *WorkflowDefinition `json:"definition,omitempty" gorm:"foreignKey:DefinitionID"`
	NodeID       string             `json:"node_id" gorm:"type:varchar(100);not null"`
	NodeType     string             `json:"node_type" gorm:"type:varchar(50);not null"` // start, end, approval, gateway
	NodeName     string             `json:"node_name" gorm:"type:varchar(100);not null"`
	NextNodes    string             `json:"next_nodes" gorm:"type:text"` // 逗号分隔的节点ID
	Assignee     string             `json:"assignee" gorm:"type:varchar(255)"` // 可以是角色、部门或具体用户ID
	Conditions   string             `json:"conditions" gorm:"type:text"` // 条件表达式，用于网关节点
}

// TableName 指定表名
func (WorkflowNode) TableName() string {
	return "workflow_nodes"
}