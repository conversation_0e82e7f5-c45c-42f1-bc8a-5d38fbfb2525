package router

import (
	"hospital-management/internal/config"
	"hospital-management/internal/controllers"
	"hospital-management/internal/middleware"
	"hospital-management/internal/services"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

func Setup(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Hospital Management System API is running",
		})
	})

	// 初始化服务
	authService := services.NewAuthService(db, cfg.JWT.Secret)
	dashboardService := services.NewDashboardService(db)

	// 初始化控制器
	authController := controllers.NewAuthController(authService)
	dashboardController := controllers.NewDashboardController(dashboardService)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关（无需认证）
		api.POST("/login", authController.Login)
		api.POST("/auth/refresh", authController.RefreshToken)

		// 需要认证的接口
		authorized := api.Group("")
		authorized.Use(middleware.JWTAuth(cfg.JWT.Secret))
		{
			// 认证相关
			authorized.POST("/auth/logout", authController.Logout)
			authorized.GET("/auth/profile", authController.GetProfile)
			authorized.POST("/auth/change-password", authController.ChangePassword)

			// 工作台相关
			authorized.GET("/dashboard/dept-head", dashboardController.GetDepartmentHeadDashboard)
			authorized.GET("/dashboard/pending-approvals", dashboardController.GetPendingApprovals)
			authorized.GET("/dashboard/budget-overview", dashboardController.GetBudgetOverview)
		}
	}

	return r
}