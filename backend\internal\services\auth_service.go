package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/jwt"
	"hospital-management/pkg/logger"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AuthService struct {
	db         *gorm.DB
	jwtSecret  string
}

func NewAuthService(db *gorm.DB, jwtSecret string) *AuthService {
	return &AuthService{
		db:         db,
		jwtSecret:  jwtSecret,
	}
}

// Login 用户登录
func (s *AuthService) Login(req *dto.LoginRequest) (*dto.LoginResponse, error) {
	// 查找用户（支持用户名、员工编号、邮箱、手机号登录）
	var user models.User
	query := s.db.Preload("Department").Where("status = ?", "active")

	// 尝试不同的登录方式
	err := query.Where("username = ? OR employee_id = ? OR email = ? OR phone = ?",
		req.Username, req.Username, req.Username, req.Username).First(&user).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名或密码错误")
		}
		logger.Error("查找用户失败:", err)
		return nil, errors.New("登录失败")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 生成JWT令牌
	jwtManager := jwt.NewJWTManager(s.jwtSecret)
	token, err := jwtManager.GenerateAccessToken(user.ID.String(), user.Username, user.DepartmentID.String(), user.Roles, nil)
	if err != nil {
		logger.Error("生成令牌失败:", err)
		return nil, errors.New("生成令牌失败")
	}

	// 构建用户响应
	userResponse := dto.UserResponse{
		ID:            user.ID,
		Username:      user.Username,
		FullName:      user.FullName,
		EmployeeID:    user.EmployeeID,
		DepartmentID:  user.DepartmentID,
		Title:         user.Title,
		Roles:         user.Roles,
		Avatar:        user.Avatar,
		Email:         user.Email,
		Phone:         user.Phone,
		CreatedAt:     user.CreatedAt,
		UpdatedAt:     user.UpdatedAt,
	}

	// 如果部门已加载，添加部门名称
	if user.Department != nil {
		userResponse.DepartmentName = user.Department.Name
	}

	// 构建用户档案
	userProfile := dto.UserProfile{
		ID:          user.ID,
		UserName:    user.Username,
		EmployeeID:  &user.EmployeeID,
		Email:       &user.Email,
		PhoneNumber: &user.Phone,
		JobTitle:    &user.Title,
		IsActive:    user.Status == "active",
	}

	// 如果部门已加载，添加部门信息
	if user.Department != nil {
		userProfile.Department = &dto.DepartmentSimpleResponse{
			ID:   user.Department.ID,
			Name: user.Department.Name,
			Code: user.Department.Code,
		}
	}

	// 返回登录响应
	return &dto.LoginResponse{
		AccessToken: token,
		TokenType:   "Bearer",
		ExpiresIn:   24 * 60 * 60, // 24小时
		User:        userProfile,
	}, nil
}

// GetUserInfo 获取当前用户信息
func (s *AuthService) GetUserInfo(userID uuid.UUID) (*dto.UserResponse, error) {
	var user models.User
	if err := s.db.Preload("Department").First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		logger.Error("获取用户信息失败:", err)
		return nil, err
	}

	// 构建用户响应
	userResponse := dto.UserResponse{
		ID:            user.ID,
		Username:      user.Username,
		FullName:      user.FullName,
		EmployeeID:    user.EmployeeID,
		DepartmentID:  user.DepartmentID,
		Title:         user.Title,
		Roles:         user.Roles,
		Avatar:        user.Avatar,
		Email:         user.Email,
		Phone:         user.Phone,
		CreatedAt:     user.CreatedAt,
		UpdatedAt:     user.UpdatedAt,
	}

	// 如果部门已加载，添加部门名称
	if user.Department != nil {
		userResponse.DepartmentName = user.Department.Name
	}

	return &userResponse, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID uuid.UUID, req *dto.ChangePasswordRequest) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.OldPassword)); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("密码加密失败:", err)
		return errors.New("密码加密失败")
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password_hash", string(hashedPassword)).Error; err != nil {
		logger.Error("密码更新失败:", err)
		return errors.New("密码更新失败")
	}

	return nil
}

// RefreshToken 刷新访问令牌
func (s *AuthService) RefreshToken(tokenString string) (string, error) {
	// 验证并刷新令牌
	jwtManager := jwt.NewJWTManager(s.jwtSecret)
	newToken, err := jwtManager.RefreshToken(tokenString)
	if err != nil {
		logger.Error("刷新令牌失败:", err)
		return "", errors.New("刷新令牌失败")
	}

	return newToken, nil
}

// Logout 用户登出
func (s *AuthService) Logout(userID uuid.UUID) error {
	// 在实际应用中，可能需要将令牌加入黑名单或进行其他操作
	// 这里简单返回成功
	return nil
}