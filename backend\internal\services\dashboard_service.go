package services

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type DashboardService struct {
	db *gorm.DB
}

func NewDashboardService(db *gorm.DB) *DashboardService {
	return &DashboardService{
		db: db,
	}
}

// GetDepartmentBudgetOverview 获取部门预算概览
func (s *DashboardService) GetDepartmentBudgetOverview(departmentID uuid.UUID, year int) (*dto.BudgetOverviewResponse, error) {
	// 如果未指定年份，则使用当前年份
	if year == 0 {
		year = time.Now().Year()
	}

	// 查询部门年度预算总额
	var totalBudget float64
	err := s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND year = ? AND status = ?", departmentID, year, "active").
		Select("COALESCE(SUM(total_amount), 0)").
		Scan(&totalBudget).Error
	if err != nil {
		logger.Error("查询部门预算总额失败:", err)
		return nil, err
	}

	// 查询已使用预算
	var usedBudget float64
	err = s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND year = ? AND status = ?", departmentID, year, "active").
		Select("COALESCE(SUM(used_amount), 0)").
		Scan(&usedBudget).Error
	if err != nil {
		logger.Error("查询已使用预算失败:", err)
		return nil, err
	}

	// 查询冻结预算
	var frozenBudget float64
	err = s.db.Model(&models.BudgetItem{}).
		Where("department_id = ? AND year = ? AND status = ?", departmentID, year, "active").
		Select("COALESCE(SUM(frozen_amount), 0)").
		Scan(&frozenBudget).Error
	if err != nil {
		logger.Error("查询冻结预算失败:", err)
		return nil, err
	}

	// 计算可用预算
	availableBudget := totalBudget - usedBudget - frozenBudget

	return &dto.BudgetOverviewResponse{
		TotalBudget:    totalBudget,
		UsedBudget:     usedBudget,
		FrozenBudget:   frozenBudget,
		AvailableBudget: availableBudget,
	}, nil
}

// GetPendingApprovals 获取待审批列表
func (s *DashboardService) GetPendingApprovals(userID uuid.UUID, limit int) ([]dto.ApprovalItemResponse, error) {
	if limit <= 0 {
		limit = 10 // 默认返回10条
	}

	// 查询用户角色和部门
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		logger.Error("查询用户信息失败:", err)
		return nil, err
	}

	// 查询待审批的工作流实例
	var workflowInstances []models.WorkflowInstance
	query := s.db.Preload("StartUser").
		Joins("JOIN workflow_nodes ON workflow_nodes.node_id = workflow_instances.current_node_id").
		Where("workflow_instances.status = ?", "active")

	// 根据用户角色和部门过滤
	// 这里简化处理，实际应用中需要根据工作流节点的assignee字段进行更复杂的匹配
	if contains(user.Roles, "department_head") {
		// 部门负责人可以看到本部门的审批
		query = query.Where("workflow_nodes.assignee = ? OR workflow_nodes.assignee = ?", 
			"department_head:"+user.DepartmentID.String(), "user:"+userID.String())
	} else {
		// 普通用户只能看到指定给自己的审批
		query = query.Where("workflow_nodes.assignee = ?", "user:"+userID.String())
	}

	if err := query.Limit(limit).Order("workflow_instances.created_at DESC").Find(&workflowInstances).Error; err != nil {
		logger.Error("查询待审批工作流实例失败:", err)
		return nil, err
	}

	// 构建响应
	result := make([]dto.ApprovalItemResponse, 0, len(workflowInstances))
	for _, instance := range workflowInstances {
		var item dto.ApprovalItemResponse
		item.SubmissionTime = instance.CreatedAt

		// 根据业务类型获取不同的业务数据
		switch instance.BusinessType {
		case "expense_claim":
			var claim models.ExpenseClaim
			if err := s.db.First(&claim, instance.BusinessID).Error; err != nil {
				logger.Error("查询报销单失败:", err)
				continue
			}
			item.Type = "报销申请"
			item.Summary = claim.Title
			item.Amount = claim.TotalAmount
			item.Link = "/expense/approvals/" + instance.BusinessID.String()
			
			// 获取申请人信息
			var applicant models.User
			if err := s.db.First(&applicant, claim.ApplicantID).Error; err == nil {
				item.Applicant = applicant.FullName
			} else {
				item.Applicant = "未知"
			}
		case "contract":
			// 合同审批处理（示例）
			item.Type = "合同审批"
			item.Summary = "合同审批 #" + instance.BusinessID.String()
			item.Link = "/contract/approvals/" + instance.BusinessID.String()
			
			if instance.StartUser != nil {
				item.Applicant = instance.StartUser.FullName
			} else {
				item.Applicant = "未知"
			}
		default:
			// 其他类型的审批
			item.Type = "其他审批"
			item.Summary = "审批 #" + instance.BusinessID.String()
			item.Link = "/approvals/" + instance.BusinessID.String()
			
			if instance.StartUser != nil {
				item.Applicant = instance.StartUser.FullName
			} else {
				item.Applicant = "未知"
			}
		}

		result = append(result, item)
	}

	return result, nil
}

// 辅助函数：检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}