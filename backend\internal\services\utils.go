package services

// 通用辅助函数

// StringValue 安全获取字符串指针的值
func StringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// IntValue 安全获取整数指针的值
func IntValue(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

// BoolValue 安全获取布尔指针的值
func BoolValue(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

// StringPtr 创建字符串指针
func StringPtr(s string) *string {
	return &s
}

// IntPtr 创建整数指针
func IntPtr(i int) *int {
	return &i
}

// BoolPtr 创建布尔指针
func BoolPtr(b bool) *bool {
	return &b
}

// Int64Value 安全获取int64指针的值
func Int64Value(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

// Int64Ptr 创建int64指针
func Int64Ptr(i int64) *int64 {
	return &i
}
