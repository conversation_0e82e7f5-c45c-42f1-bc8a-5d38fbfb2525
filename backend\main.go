package main

import (
	"hospital-management/internal/config"
	"hospital-management/internal/database"
	"hospital-management/internal/router"
	"hospital-management/pkg/logger"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// @title 医院内部控制与运营管理系统 API
// @version 1.0
// @description 医院内部控制与运营管理系统的后端API服务
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 初始化日志
	logger.Setup(cfg.Log.Level, cfg.Log.File)

	// 设置Gin模式
	gin.SetMode("release")

	// 初始化数据库
	logger.Info("Connecting to database...")
	db, err := database.Connect(cfg.Database)
	if err != nil {
		logger.Error("Failed to connect to database:", err)
		log.Fatal("Failed to connect to database:", err)
	}
	logger.Info("Database connected successfully")

	// 注释掉智能迁移数据库表的代码
	// logger.Info("Starting database migration...")
	// if err := database.SmartMigrate(db); err != nil {
	// 	logger.Error("Failed to migrate database:", err)
	// 	log.Fatal("Failed to migrate database:", err)
	// }
	// logger.Info("Database migration completed")

	// 初始化路由
	r := router.Setup(db, cfg)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:           cfg.Server.Host + ":" + cfg.Server.Port,
		Handler:        r,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   10 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	logger.Info("Server starting on " + cfg.Server.Host + ":" + cfg.Server.Port)

	// 启动服务器
	if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatal("Failed to start server:", err)
	}
}