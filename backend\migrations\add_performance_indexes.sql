-- 性能优化索引
-- 为了提升查询性能，添加以下索引

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_department_id ON tbl_users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON tbl_users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_user_name ON tbl_users(user_name);
CREATE INDEX IF NOT EXISTS idx_users_employee_id ON tbl_users(employee_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON tbl_users(email);

-- 部门表索引
CREATE INDEX IF NOT EXISTS idx_departments_parent_id ON tbl_departments(parent_id);
CREATE INDEX IF NOT EXISTS idx_departments_is_active ON tbl_departments(is_active);
CREATE INDEX IF NOT EXISTS idx_departments_dept_type ON tbl_departments(dept_type);
CREATE INDEX IF NOT EXISTS idx_departments_manager_id ON tbl_departments(manager_id);

-- 角色表索引
CREATE INDEX IF NOT EXISTS idx_roles_is_active ON tbl_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_roles_role_code ON tbl_roles(role_code);

-- 用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON tbl_user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON tbl_user_roles(role_id);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_users_dept_active ON tbl_users(department_id, is_active);
CREATE INDEX IF NOT EXISTS idx_departments_parent_active ON tbl_departments(parent_id, is_active);

-- 全文搜索索引（如果支持）
-- CREATE INDEX IF NOT EXISTS idx_users_search ON tbl_users USING gin(to_tsvector('simple', user_name || ' ' || COALESCE(employee_id, '') || ' ' || COALESCE(email, '')));
-- CREATE INDEX IF NOT EXISTS idx_departments_search ON tbl_departments USING gin(to_tsvector('simple', name || ' ' || COALESCE(code, '')));
