-- 确保部门表有所有必要的字段
ALTER TABLE tbl_departments 
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS description TEXT;

-- 更新现有部门数据，添加缺失的字段值
UPDATE tbl_departments 
SET 
    dept_type = 'administrative',
    description = '负责医院行政管理工作',
    contact_phone = '010-12345678',
    contact_email = '<EMAIL>'
WHERE name = '院办' AND (dept_type IS NULL OR dept_type = '');

UPDATE tbl_departments 
SET 
    dept_type = 'administrative',
    description = '负责医院信息化建设和维护',
    contact_phone = '010-12345679',
    contact_email = '<EMAIL>'
WHERE name = '信息科' AND (dept_type IS NULL OR dept_type = '');

UPDATE tbl_departments 
SET 
    dept_type = 'administrative',
    description = '负责医院财务管理和预算控制',
    contact_phone = '010-12345680',
    contact_email = '<EMAIL>'
WHERE name = '财务科' AND (dept_type IS NULL OR dept_type = '');

UPDATE tbl_departments 
SET 
    dept_type = 'clinical',
    description = '负责心血管疾病的诊断和治疗',
    contact_phone = '010-12345681',
    contact_email = '<EMAIL>'
WHERE name = '心血管内科' AND (dept_type IS NULL OR dept_type = '');

-- 添加一些子部门来测试树形结构
INSERT INTO tbl_departments (id, parent_id, name, code, dept_type, description, contact_phone, contact_email, is_active, created_at, updated_at)
VALUES 
    (gen_random_uuid(), 
     (SELECT id FROM tbl_departments WHERE name = '心血管内科' LIMIT 1),
     '心血管内科一病区', 
     'D_CARDIO_1', 
     'clinical', 
     '心血管内科第一病区',
     '010-12345682',
     '<EMAIL>',
     true, 
     NOW(), 
     NOW())
ON CONFLICT DO NOTHING;

INSERT INTO tbl_departments (id, parent_id, name, code, dept_type, description, contact_phone, contact_email, is_active, created_at, updated_at)
VALUES 
    (gen_random_uuid(), 
     (SELECT id FROM tbl_departments WHERE name = '心血管内科' LIMIT 1),
     '心血管内科二病区', 
     'D_CARDIO_2', 
     'clinical', 
     '心血管内科第二病区',
     '010-12345683',
     '<EMAIL>',
     true, 
     NOW(), 
     NOW())
ON CONFLICT DO NOTHING;
