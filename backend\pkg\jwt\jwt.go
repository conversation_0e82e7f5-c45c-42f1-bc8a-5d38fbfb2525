package jwt

import (
	"errors"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

const (
	TokenExpireDuration = time.Hour * 24 // 令牌有效期24小时
)

// CustomClaims 自定义JWT声明
type CustomClaims struct {
	UserID       string   `json:"user_id"`
	UserName     string   `json:"user_name"`
	DepartmentID string   `json:"department_id"`
	Roles        []string `json:"roles"`
	Permissions  []string `json:"permissions"`
	TokenType    string   `json:"token_type"` // "access" 或 "refresh"
	jwt.RegisteredClaims
}

// JWTManager JWT管理器
type JWTManager struct {
	secretKey string
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secretKey string) *JWTManager {
	return &JWTManager{
		secretKey: secretKey,
	}
}

// GenerateToken 生成JWT令牌
func (m *JWTManager) GenerateToken(userID, userName, departmentID string, roles, permissions []string, tokenType string, duration time.Duration) (string, error) {
	// 创建自定义声明
	claims := CustomClaims{
		UserID:       userID,
		UserName:     userName,
		DepartmentID: departmentID,
		Roles:        roles,
		Permissions:  permissions,
		TokenType:    tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(duration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "hospital-management-system",
		},
	}

	// 使用指定的签名方法创建签名对象
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 使用指定的secret签名并获得完整的编码后的字符串token
	return token.SignedString([]byte(m.secretKey))
}

// GenerateAccessToken 生成访问令牌
func (m *JWTManager) GenerateAccessToken(userID, userName, departmentID string, roles, permissions []string) (string, error) {
	return m.GenerateToken(userID, userName, departmentID, roles, permissions, "access", TokenExpireDuration)
}

// GenerateRefreshToken 生成刷新令牌
func (m *JWTManager) GenerateRefreshToken(userID, userName string) (string, error) {
	return m.GenerateToken(userID, userName, "", nil, nil, "refresh", TokenExpireDuration*7) // 刷新令牌有效期7天
}

// ValidateToken 验证JWT令牌
func (m *JWTManager) ValidateToken(tokenString string) (*CustomClaims, error) {
	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(m.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	// 校验token
	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// RefreshToken 刷新JWT令牌
func (m *JWTManager) RefreshToken(refreshToken string) (string, error) {
	// 验证刷新令牌
	claims, err := m.ValidateToken(refreshToken)
	if err != nil {
		return "", err
	}

	// 检查令牌类型
	if claims.TokenType != "refresh" {
		return "", errors.New("无效的刷新令牌类型")
	}

	// 生成新的访问令牌
	return m.GenerateAccessToken(claims.UserID, claims.UserName, claims.DepartmentID, claims.Roles, claims.Permissions)
}

// ExtractTokenFromHeader 从Authorization头中提取令牌
func ExtractTokenFromHeader(authHeader string) string {
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return ""
	}
	return parts[1]
}
