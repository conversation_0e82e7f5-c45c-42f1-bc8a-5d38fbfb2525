package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var log *zap.SugaredLogger

// Setup 初始化日志记录器
func Setup(level, filePath string) {
	// 创建日志目录
	if filePath != "" {
		dir := filepath.Dir(filePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			fmt.Printf("创建日志目录失败: %v\n", err)
		}
	}

	// 设置日志级别
	var zapLevel zapcore.Level
	switch strings.ToLower(level) {
	case "debug":
		zapLevel = zapcore.DebugLevel
	case "info":
		zapLevel = zapcore.InfoLevel
	case "warn":
		zapLevel = zapcore.WarnLevel
	case "error":
		zapLevel = zapcore.ErrorLevel
	default:
		zapLevel = zapcore.InfoLevel
	}

	// 配置编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 配置输出
	var cores []zapcore.Core

	// 控制台输出
	consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)
	consoleCore := zapcore.NewCore(
		consoleEncoder,
		zapcore.AddSync(os.Stdout),
		zapLevel,
	)
	cores = append(cores, consoleCore)

	// 文件输出
	if filePath != "" {
		fileWriter := &lumberjack.Logger{
			Filename:   filePath,
			MaxSize:    100, // MB
			MaxBackups: 10,
			MaxAge:     30, // 天
			Compress:   true,
		}
		fileEncoder := zapcore.NewJSONEncoder(encoderConfig)
		fileCore := zapcore.NewCore(
			fileEncoder,
			zapcore.AddSync(fileWriter),
			zapLevel,
		)
		cores = append(cores, fileCore)
	}

	// 创建日志记录器
	core := zapcore.NewTee(cores...)
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	log = logger.Sugar()

	Info("日志系统初始化完成，级别:", level)
}

// Debug 记录调试级别日志
func Debug(args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Debug(args...)
}

// Debugf 记录格式化的调试级别日志
func Debugf(format string, args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Debugf(format, args...)
}

// Info 记录信息级别日志
func Info(args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Info(args...)
}

// Infof 记录格式化的信息级别日志
func Infof(format string, args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Infof(format, args...)
}

// Warn 记录警告级别日志
func Warn(args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Warn(args...)
}

// Warnf 记录格式化的警告级别日志
func Warnf(format string, args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Warnf(format, args...)
}

// Error 记录错误级别日志
func Error(args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Error(args...)
}

// Errorf 记录格式化的错误级别日志
func Errorf(format string, args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Errorf(format, args...)
}

// Fatal 记录致命级别日志并退出程序
func Fatal(args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Fatal(args...)
}

// Fatalf 记录格式化的致命级别日志并退出程序
func Fatalf(format string, args ...interface{}) {
	if log == nil {
		setupDefaultLogger()
	}
	log.Fatalf(format, args...)
}

// 设置默认日志记录器
func setupDefaultLogger() {
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建控制台输出
	consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)
	consoleCore := zapcore.NewCore(
		consoleEncoder,
		zapcore.AddSync(os.Stdout),
		zapcore.InfoLevel,
	)

	// 创建日志记录器
	logger := zap.New(consoleCore, zap.AddCaller(), zap.AddCallerSkip(1))
	log = logger.Sugar()

	// 记录日志系统初始化信息
	now := time.Now().Format("2006-01-02 15:04:05")
	fmt.Printf("[%s] [INFO] 使用默认日志配置初始化日志系统\n", now)
}