package validator

import (
	"reflect"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// NewValidator 创建一个新的验证器实例，并注册自定义验证规则
func NewValidator() *validator.Validate {
	validate := validator.New()

	// 注册 UUID 验证器
	validate.RegisterValidation("uuid", validateUUID)

	return validate
}

// validateUUID 验证 UUID 格式
func validateUUID(fl validator.FieldLevel) bool {
	field := fl.Field()

	// 处理不同类型的字段
	switch field.Kind() {
	case reflect.String:
		// 字符串类型
		value := field.String()
		if value == "" {
			return true // 空值由 omitempty 处理
		}
		_, err := uuid.Parse(value)
		return err == nil

	case reflect.Ptr:
		// 指针类型（如 *uuid.UUID）
		if field.IsNil() {
			return true // nil 指针由 omitempty 处理
		}

		// 检查指针指向的类型
		elem := field.Elem()
		if elem.Type() == reflect.TypeOf(uuid.UUID{}) {
			// 这是一个 *uuid.UUID，直接返回 true（因为它已经是有效的 UUID）
			return true
		}

		// 如果是指向字符串的指针
		if elem.Kind() == reflect.String {
			value := elem.String()
			if value == "" {
				return true
			}
			_, err := uuid.Parse(value)
			return err == nil
		}

	case reflect.Struct:
		// 如果是 uuid.UUID 结构体
		if field.Type() == reflect.TypeOf(uuid.UUID{}) {
			return true // uuid.UUID 类型本身就是有效的
		}
	}

	return false
}
