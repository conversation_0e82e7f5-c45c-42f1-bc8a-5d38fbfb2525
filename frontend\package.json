{"name": "hospital-management-frontend", "version": "1.0.0", "description": "医院内部控制与运营管理系统前端", "scripts": {"dev": "vite", "build": "vite build", "build-with-check": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2": "^5.3.5", "@vueuse/core": "^10.4.1", "ant-design-vue": "^4.0.0", "axios": "^1.5.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "lodash-es": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/lodash-es": "^4.17.9", "@types/node": "^20.5.9", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "typescript": "^5.2.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}