import request from './request'
import type { LoginForm, LoginResponseData, UserInfo, ApiResponse } from '@/types/auth'

// 用户登录
export const login = (data: LoginForm) => {
  return request.post<ApiResponse<LoginResponseData>>('/api/v1/login', data)
}

// 获取用户信息
export const getUserInfo = () => {
  return request.get<ApiResponse<UserInfo>>('/api/v1/user/info')
}

// SSO登录回调
export const ssoCallback = (code: string) => {
  return request.post<ApiResponse<LoginResponseData>>('/api/v1/auth/sso/callback', { code })
}

// 刷新token
export const refreshToken = () => {
  return request.post<ApiResponse<{ token: string }>>('/api/v1/auth/refresh')
}
