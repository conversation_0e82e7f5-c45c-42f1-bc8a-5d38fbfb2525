import request from './request'
import type { BudgetOverviewResponse, ApprovalItemResponse, DashboardOverviewResponse } from '@/types/dashboard'

// 获取部门负责人工作台数据
export const getDeptHeadDashboard = (year?: number) => {
  return request.get<DashboardOverviewResponse>('/api/v1/dashboard/dept-head', {
    params: year ? { year } : undefined
  })
}

// 获取待审批列表
export const getPendingApprovals = (limit?: number) => {
  return request.get<ApprovalItemResponse[]>('/api/v1/dashboard/pending-approvals', {
    params: { limit }
  })
}

// 获取预算概览
export const getBudgetOverview = (departmentId?: string, year?: number) => {
  return request.get<BudgetOverviewResponse>('/api/v1/dashboard/budget-overview', {
    params: { departmentId, year }
  })
}
