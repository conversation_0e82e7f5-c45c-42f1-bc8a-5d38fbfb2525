import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加token
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { status } = response;
    const responseData = response.data;
    
    // 标准RESTful API：根据HTTP状态码判断成功/失败
    if (status >= 200 && status < 300) {
      // HTTP 2xx 表示成功，直接返回响应数据
      return responseData;
    }
    
    // HTTP状态码不是2xx，交给错误处理器处理
    return Promise.reject(response);
  },
  (error) => {
    // 获取详细的错误信息
    let errorMsg = '';
    
    if (error.response) {
      // 服务器响应了，但状态码不在 2xx 范围内
      const responseData = error.response.data;
      
      // 尝试从不同的字段中提取错误信息
      if (typeof responseData === 'object') {
        errorMsg = responseData.message || responseData.error || responseData.msg || JSON.stringify(responseData);
      } else if (typeof responseData === 'string') {
        errorMsg = responseData;
      }
      
      // 如果没有提取到具体错误信息，则使用状态码
      if (!errorMsg) {
        errorMsg = `服务器错误 (${error.response.status})`;
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      errorMsg = '服务器无响应，请检查网络连接';
    } else {
      // 请求设置时发生错误
      errorMsg = error.message || '请求错误';
    }
    
    // 显示错误信息
    message.error(errorMsg);
    
    // 处理 401 认证错误
    if (error.response?.status === 401) {
      // 如果当前不在登录页，才清除token并跳转
      if (!window.location.pathname.endsWith('/login')) {
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        // 使用基础路径
        const basePath = import.meta.env.BASE_URL || '/'
        window.location.href = `${basePath}login`.replace('//', '/')
      }
    }
    
    return Promise.reject(error)
  }
)

export default request
