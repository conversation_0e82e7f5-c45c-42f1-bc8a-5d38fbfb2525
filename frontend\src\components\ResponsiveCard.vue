<template>
  <a-card 
    :class="['responsive-card', { 'responsive-card-mobile': isMobile }]"
    v-bind="$attrs"
  >
    <template v-if="$slots.title" #title>
      <slot name="title" />
    </template>
    
    <template v-if="$slots.extra" #extra>
      <div :class="['card-extra', { 'card-extra-mobile': isMobile }]">
        <slot name="extra" />
      </div>
    </template>
    
    <slot />
  </a-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const isMobile = ref(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.responsive-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s;
}

.responsive-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端卡片优化 */
.responsive-card-mobile {
  margin: 0 -8px 16px -8px;
  border-radius: 0;
  box-shadow: none;
  border-bottom: 1px solid #f0f0f0;
}

.responsive-card-mobile:hover {
  box-shadow: none;
}

/* 卡片头部样式 */
.responsive-card :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.responsive-card-mobile :deep(.ant-card-head) {
  padding: 12px 16px;
}

.responsive-card :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.responsive-card-mobile :deep(.ant-card-head-title) {
  font-size: 14px;
}

/* 卡片内容样式 */
.responsive-card :deep(.ant-card-body) {
  padding: 24px;
}

.responsive-card-mobile :deep(.ant-card-body) {
  padding: 16px;
}

/* 卡片额外内容样式 */
.card-extra {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.card-extra-mobile {
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
  width: 100%;
}

.card-extra-mobile :deep(.ant-space) {
  flex-direction: column;
  width: 100%;
}

.card-extra-mobile :deep(.ant-btn) {
  width: 100%;
  margin-bottom: 4px;
}

/* 按钮组优化 */
.card-extra :deep(.ant-space) {
  flex-wrap: wrap;
}

.card-extra :deep(.ant-btn) {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.card-extra :deep(.ant-btn-primary) {
  background: #1890ff;
  border-color: #1890ff;
}

.card-extra :deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.card-extra :deep(.ant-btn:not(.ant-btn-primary):hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 平板端优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .responsive-card :deep(.ant-card-head) {
    padding: 14px 20px;
  }
  
  .responsive-card :deep(.ant-card-body) {
    padding: 20px;
  }
  
  .card-extra :deep(.ant-btn) {
    font-size: 13px;
    padding: 4px 12px;
  }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .responsive-card-mobile {
    margin: 0 -16px 16px -16px;
  }
  
  .responsive-card-mobile :deep(.ant-card-head) {
    padding: 10px 12px;
  }
  
  .responsive-card-mobile :deep(.ant-card-body) {
    padding: 12px;
  }
  
  .responsive-card-mobile :deep(.ant-card-head-title) {
    font-size: 13px;
  }
}

/* 加载状态优化 */
.responsive-card :deep(.ant-spin-container) {
  min-height: 100px;
}

.responsive-card-mobile :deep(.ant-spin-container) {
  min-height: 80px;
}

/* 空状态优化 */
.responsive-card :deep(.ant-empty) {
  margin: 20px 0;
}

.responsive-card-mobile :deep(.ant-empty) {
  margin: 16px 0;
}

.responsive-card :deep(.ant-empty-description) {
  color: #8c8c8c;
  font-size: 14px;
}

.responsive-card-mobile :deep(.ant-empty-description) {
  font-size: 12px;
}

/* 分割线优化 */
.responsive-card :deep(.ant-divider) {
  margin: 16px 0;
}

.responsive-card-mobile :deep(.ant-divider) {
  margin: 12px 0;
}

/* 统计数据样式 */
.responsive-card :deep(.ant-statistic) {
  text-align: center;
}

.responsive-card :deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.responsive-card :deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.responsive-card-mobile :deep(.ant-statistic-content) {
  font-size: 20px;
}

/* 标签样式 */
.responsive-card :deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.responsive-card-mobile :deep(.ant-tag) {
  font-size: 11px;
  padding: 1px 6px;
}
</style>
