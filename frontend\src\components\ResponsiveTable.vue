<template>
  <div class="responsive-table-wrapper">
    <a-table
      v-bind="$attrs"
      :columns="processedColumns"
      :scroll="{ x: minWidth, ...($attrs.scroll || {}) }"
      :bordered="true"
      :size="tableSize"
      :pagination="paginationConfig"
      @change="handleTableChange"
    >
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'

interface Props {
  columns: any[]
  minWidth?: number
  mobileHiddenColumns?: string[]
  pagination?: any
}

const props = withDefaults(defineProps<Props>(), {
  minWidth: 800,
  mobileHiddenColumns: () => [],
  pagination: () => ({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`
  })
})

const emit = defineEmits(['change'])

const isMobile = ref(false)
const isTablet = ref(false)

// 检测设备类型
const checkDevice = () => {
  const width = window.innerWidth
  isMobile.value = width <= 768
  isTablet.value = width > 768 && width <= 1024
}

// 处理表格列配置
const processedColumns = computed(() => {
  return props.columns.map(column => {
    const processed = { ...column }
    
    // 添加移动端隐藏类名
    if (props.mobileHiddenColumns.includes(column.key) || column.mobileHidden) {
      processed.className = `${processed.className || ''} mobile-hidden`.trim()
    }
    
    // 操作列添加特殊类名
    if (column.key === 'action') {
      processed.className = `${processed.className || ''} action-column`.trim()
    }
    
    // 添加文本省略
    if (column.ellipsis === undefined && column.width) {
      processed.ellipsis = true
    }
    
    return processed
  })
})

// 计算表格尺寸
const tableSize = computed(() => {
  if (isMobile.value) return 'small'
  if (isTablet.value) return 'middle'
  return 'middle'
})

// 分页配置
const paginationConfig = computed(() => {
  if (props.pagination === false) return false

  const defaultPagination = {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: !isMobile.value,
    showQuickJumper: !isMobile.value,
    showTotal: isMobile.value ? undefined : (total: number) => `共 ${total} 条记录`,
    size: isMobile.value ? 'small' : 'default'
  }

  return { ...defaultPagination, ...props.pagination }
})

// 处理表格变化事件
const handleTableChange = (...args: any[]) => {
  emit('change', ...args)
}

// 监听窗口大小变化
const handleResize = () => {
  checkDevice()
}

onMounted(() => {
  checkDevice()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.responsive-table-wrapper {
  width: 100%;
  overflow: hidden;
}

/* 表格滚动优化 */
.responsive-table-wrapper :deep(.ant-table-container) {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.responsive-table-wrapper :deep(.ant-table-body) {
  overflow-x: visible;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .responsive-table-wrapper :deep(.ant-table) {
    font-size: 12px;
  }
  
  .responsive-table-wrapper :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
    font-size: 12px;
  }

  .responsive-table-wrapper :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
    font-size: 12px;
  }

  /* 隐藏移动端不需要的列 */
  .responsive-table-wrapper :deep(.mobile-hidden) {
    display: none !important;
  }

  /* 操作列固定 */
  .responsive-table-wrapper :deep(.action-column) {
    display: table-cell !important;
    position: sticky;
    right: 0;
    background: white;
    z-index: 1;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  }

  /* 移动端分页优化 */
  .responsive-table-wrapper :deep(.ant-pagination) {
    text-align: center;
    margin-top: 16px;
  }

  .responsive-table-wrapper :deep(.ant-pagination-options) {
    display: none;
  }

  .responsive-table-wrapper :deep(.ant-pagination-total-text) {
    display: none;
  }
}

/* 平板端优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .responsive-table-wrapper :deep(.ant-table) {
    font-size: 13px;
  }

  .responsive-table-wrapper :deep(.ant-table-thead > tr > th) {
    padding: 10px 6px;
  }

  .responsive-table-wrapper :deep(.ant-table-tbody > tr > td) {
    padding: 10px 6px;
  }
}

/* 表格样式优化 */
.responsive-table-wrapper :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
}

.responsive-table-wrapper :deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
}

.responsive-table-wrapper :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 状态标签样式 */
.responsive-table-wrapper :deep(.ant-tag) {
  margin: 0;
  font-size: 11px;
  padding: 2px 6px;
}

@media (max-width: 768px) {
  .responsive-table-wrapper :deep(.ant-tag) {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 滚动条样式 */
.responsive-table-wrapper :deep(.ant-table-container)::-webkit-scrollbar {
  height: 6px;
}

.responsive-table-wrapper :deep(.ant-table-container)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.responsive-table-wrapper :deep(.ant-table-container)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.responsive-table-wrapper :deep(.ant-table-container)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
