<template>
  <div class="search-form">
    <a-form 
      layout="inline" 
      :class="['search-form-inner', { 'search-form-mobile': isMobile }]"
      v-bind="$attrs"
    >
      <slot />
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const isMobile = ref(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.search-form-inner {
  width: 100%;
}

/* 移动端优化 */
.search-form-mobile :deep(.ant-form-item) {
  margin-bottom: 12px;
  width: 100%;
  margin-right: 0;
}

.search-form-mobile :deep(.ant-form-item-control) {
  width: 100%;
}

.search-form-mobile :deep(.ant-input),
.search-form-mobile :deep(.ant-select),
.search-form-mobile :deep(.ant-tree-select),
.search-form-mobile :deep(.ant-date-picker),
.search-form-mobile :deep(.ant-range-picker) {
  width: 100% !important;
}

.search-form-mobile :deep(.ant-form-item-label) {
  text-align: left !important;
  padding-bottom: 4px;
}

.search-form-mobile :deep(.ant-space) {
  width: 100%;
  justify-content: center;
}

.search-form-mobile :deep(.ant-space .ant-btn) {
  flex: 1;
  max-width: 120px;
}

/* 平板端优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .search-form :deep(.ant-form-item) {
    margin-bottom: 8px;
  }
  
  .search-form :deep(.ant-input),
  .search-form :deep(.ant-select),
  .search-form :deep(.ant-tree-select) {
    min-width: 150px;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .search-form :deep(.ant-form-item) {
    margin-bottom: 0;
    margin-right: 16px;
  }
  
  .search-form :deep(.ant-form-item:last-child) {
    margin-right: 0;
  }
}

/* 表单项标签样式 */
.search-form :deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #333;
}

/* 按钮组样式 */
.search-form :deep(.ant-space) {
  flex-wrap: wrap;
  gap: 8px;
}

.search-form :deep(.ant-btn) {
  border-radius: 4px;
  font-weight: 500;
}

.search-form :deep(.ant-btn-primary) {
  background: #1890ff;
  border-color: #1890ff;
}

.search-form :deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 输入框样式 */
.search-form :deep(.ant-input),
.search-form :deep(.ant-select-selector),
.search-form :deep(.ant-tree-select-selector) {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.search-form :deep(.ant-input:focus),
.search-form :deep(.ant-select-focused .ant-select-selector),
.search-form :deep(.ant-tree-select-focused .ant-tree-select-selector) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 清除按钮样式 */
.search-form :deep(.ant-input-clear-icon),
.search-form :deep(.ant-select-clear) {
  color: #bfbfbf;
  transition: color 0.3s;
}

.search-form :deep(.ant-input-clear-icon:hover),
.search-form :deep(.ant-select-clear:hover) {
  color: #666;
}
</style>
