<template>
  <div class="table-actions">
    <slot />
  </div>
</template>

<script setup lang="ts">
// 这是一个简单的包装组件，主要用于应用样式
</script>

<style scoped>
.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
}

.table-actions :deep(.ant-btn) {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
  border: none;
  box-shadow: none;
}

.table-actions :deep(.ant-btn:hover) {
  background-color: #f0f0f0;
}

.table-actions :deep(.ant-btn-link) {
  padding: 0 4px;
}

.table-actions :deep(.ant-btn-dangerous:hover) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .table-actions {
    flex-direction: column;
    gap: 2px;
    align-items: stretch;
  }
  
  .table-actions :deep(.ant-btn) {
    padding: 0 4px;
    height: 20px;
    font-size: 11px;
    width: 100%;
    text-align: center;
  }
  
  .table-actions :deep(.ant-popconfirm) {
    width: 100%;
  }
  
  .table-actions :deep(.ant-popconfirm .ant-btn) {
    width: 100%;
  }
}

/* 平板端优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .table-actions :deep(.ant-btn) {
    padding: 0 6px;
    height: 22px;
    font-size: 11px;
  }
}
</style>
