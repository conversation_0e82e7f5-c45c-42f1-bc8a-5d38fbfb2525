import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DeptHeadDashboard.vue'),
        meta: {
          title: '工作台',
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory('/hospital/'),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const basePath = router.options.history.base || '/'

  if (to.meta.requiresAuth && !token) {
    // 使用完整路径进行重定向
    next({ path: '/login', replace: true })
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页面，重定向到工作台
    next({ path: '/dashboard', replace: true })
  } else {
    next()
  }
})

export default router