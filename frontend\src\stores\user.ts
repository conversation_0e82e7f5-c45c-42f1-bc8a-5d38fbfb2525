import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, getUserInfo } from '@/api/auth'
import type { LoginForm, UserInfo } from '@/types/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const userName = ref<string>('')
  const userRoles = ref<string[]>([])
  const userDepartment = ref<string>('')

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      // 由于响应拦截器已经返回了data部分，直接使用response
      const { access_token, user } = response
      
      // 保存token
      token.value = access_token
      localStorage.setItem('token', access_token)
      
      // 从用户档案中提取信息并构建userInfo对象
      userInfo.value = {
        id: user.id,
        username: user.user_name,
        fullName: user.user_name, // 使用user_name作为fullName
        employeeId: user.employee_id || '',
        departmentId: user.department?.id || '',
        departmentName: user.department?.name || '',
        title: user.job_title || '',
        roles: user.roles?.map(r => r.code) || [], // 从roles数组中提取code，如果没有则使用空数组
        avatar: '', // 后端没有返回avatar
        email: user.email || '',
        phone: user.phone_number || '',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      userName.value = user.user_name
      userRoles.value = [] // 后端没有返回roles数组，使用空数组
      userDepartment.value = user.department?.name || ''
      
      // 保存用户信息到localStorage
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      
      // 处理记住密码
      if (loginForm.rememberMe) {
        localStorage.setItem('rememberMe', 'true')
        localStorage.setItem('username', loginForm.username)
      } else {
        localStorage.removeItem('rememberMe')
        localStorage.removeItem('username')
      }
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      const info = response.data
      
      userInfo.value = info
      userName.value = info.fullName
      userRoles.value = info.roles || []
      userDepartment.value = info.departmentName || ''
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 退出登录
  const logoutAction = async () => {
    token.value = ''
    userInfo.value = null
    userName.value = ''
    userRoles.value = []
    userDepartment.value = ''
    
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  // 初始化用户信息（从localStorage恢复）
  const initUserInfo = () => {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
      // 可以在这里调用getUserInfoAction来获取最新用户信息
    }
  }

  return {
    token,
    userInfo,
    userName,
    userRoles,
    userDepartment,
    loginAction,
    getUserInfoAction,
    logoutAction,
    initUserInfo
  }
})