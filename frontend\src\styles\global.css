/* 全局响应式样式 */

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 响应式断点 */
:root {
  --mobile-breakpoint: 768px;
  --tablet-breakpoint: 1024px;
  --desktop-breakpoint: 1200px;
}

/* 表格响应式处理 - 由ResponsiveTable组件处理 */

/* 移动端表格优化 */
@media (max-width: 768px) {
  .ant-table {
    min-width: 600px;
    font-size: 12px;
  }
  
  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  /* 隐藏不重要的列 */
  .ant-table-tbody > tr > td:nth-child(n+6),
  .ant-table-thead > tr > th:nth-child(n+6) {
    display: none;
  }
  
  /* 保留操作列 */
  .ant-table-tbody > tr > td:last-child,
  .ant-table-thead > tr > th:last-child {
    display: table-cell !important;
  }
}

/* 搜索表单响应式 */
.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

@media (max-width: 768px) {
  .search-form .ant-form-inline .ant-form-item {
    margin-bottom: 8px;
    width: 100%;
  }
  
  .search-form .ant-input,
  .search-form .ant-select,
  .search-form .ant-tree-select {
    width: 100% !important;
  }
}

/* 卡片响应式 */
.ant-card {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .ant-card {
    margin: 0 -8px 16px -8px;
    border-radius: 0;
  }
  
  .ant-card-head {
    padding: 0 16px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
}

/* 按钮组响应式 */
.ant-space {
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .ant-card-head .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .ant-card-head .ant-space .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 布局响应式优化 */
@media (max-width: 768px) {
  .layout .sider {
    position: fixed !important;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .layout .sider.ant-layout-sider-collapsed {
    transform: translateX(-80px);
  }
  
  .layout .main-layout {
    margin-left: 0 !important;
    width: 100%;
  }
  
  .layout .header {
    padding: 0 16px;
  }
  
  .layout .content {
    padding: 16px;
  }
}

/* 表单响应式 */
@media (max-width: 768px) {
  .ant-modal {
    margin: 0;
    max-width: 100vw;
    top: 0;
  }
  
  .ant-modal-content {
    border-radius: 0;
  }
  
  .ant-form-item-label {
    text-align: left !important;
  }
  
  .ant-col-6 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  
  .ant-col-18 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
}

/* 分页响应式 */
@media (max-width: 768px) {
  .ant-pagination {
    text-align: center;
  }
  
  .ant-pagination-options {
    display: none;
  }
  
  .ant-pagination-total-text {
    display: none;
  }
}

/* 面包屑响应式 */
@media (max-width: 768px) {
  .breadcrumb {
    display: none;
  }
}

/* 标签响应式 */
@media (max-width: 768px) {
  .ant-tag {
    margin-bottom: 4px;
    font-size: 11px;
    padding: 0 4px;
  }
}

/* 统计卡片响应式 */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }
}

/* 图表响应式 */
.chart-container {
  width: 100%;
  height: 400px;
}

@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }
}

/* 工具栏响应式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar > * {
    width: 100%;
  }
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态优化 */
.ant-spin-container {
  min-height: 200px;
}

/* 空状态优化 */
.ant-empty {
  margin: 40px 0;
}

@media (max-width: 768px) {
  .ant-empty {
    margin: 20px 0;
  }
  
  .ant-empty-description {
    font-size: 12px;
  }
}

/* 消息提示响应式 */
@media (max-width: 768px) {
  .ant-message {
    top: 60px;
  }
}

/* 抽屉响应式 */
@media (max-width: 768px) {
  .ant-drawer-content-wrapper {
    width: 100vw !important;
  }
}

/* 下拉菜单响应式 */
@media (max-width: 768px) {
  .ant-dropdown-menu {
    max-width: calc(100vw - 32px);
  }
}
