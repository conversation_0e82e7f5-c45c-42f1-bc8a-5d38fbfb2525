/* 表格响应式样式优化 - 已移至ResponsiveTable组件 */

/* 表格头部样式 */
.responsive-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  padding: 12px 8px;
}

/* 表格内容样式 */
.responsive-table .ant-table-tbody > tr > td {
  padding: 12px 8px;
  vertical-align: middle;
}

.responsive-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 移动端表格优化 */
@media (max-width: 768px) {
  .responsive-table .ant-table {
    min-width: 600px;
    font-size: 12px;
  }
  
  .responsive-table .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .responsive-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  /* 隐藏次要列 */
  .responsive-table .ant-table-thead > tr > th.mobile-hidden,
  .responsive-table .ant-table-tbody > tr > td.mobile-hidden {
    display: none;
  }
  
  /* 操作列始终显示 */
  .responsive-table .ant-table-thead > tr > th.action-column,
  .responsive-table .ant-table-tbody > tr > td.action-column {
    display: table-cell !important;
    position: sticky;
    right: 0;
    background: white;
    z-index: 1;
  }
}

/* 平板端表格优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .responsive-table .ant-table {
    min-width: 700px;
    font-size: 13px;
  }
  
  .responsive-table .ant-table-thead > tr > th {
    padding: 10px 6px;
  }
  
  .responsive-table .ant-table-tbody > tr > td {
    padding: 10px 6px;
  }
}

/* 表格内容溢出处理 */
.table-cell-ellipsis {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .table-cell-ellipsis {
    max-width: 80px;
  }
}

/* 状态标签样式 */
.responsive-table .ant-tag {
  margin: 0;
  font-size: 11px;
  padding: 2px 6px;
}

@media (max-width: 768px) {
  .responsive-table .ant-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 操作按钮样式 */
.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.table-actions .ant-btn {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
}

@media (max-width: 768px) {
  .table-actions {
    flex-direction: column;
    gap: 2px;
  }
  
  .table-actions .ant-btn {
    padding: 0 4px;
    height: 20px;
    font-size: 11px;
  }
}

/* 金额显示样式 */
.amount-text {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #1890ff;
}

/* 表格分页样式 */
.responsive-table + .ant-pagination {
  margin-top: 16px;
  text-align: right;
}

@media (max-width: 768px) {
  .responsive-table + .ant-pagination {
    text-align: center;
  }
  
  .responsive-table + .ant-pagination .ant-pagination-options {
    display: none;
  }
  
  .responsive-table + .ant-pagination .ant-pagination-total-text {
    display: none;
  }
  
  .responsive-table + .ant-pagination .ant-pagination-jump-prev,
  .responsive-table + .ant-pagination .ant-pagination-jump-next {
    display: none;
  }
}

/* 表格加载状态 */
.responsive-table .ant-spin-container {
  min-height: 200px;
}

/* 表格空状态 */
.responsive-table .ant-empty {
  margin: 40px 0;
}

@media (max-width: 768px) {
  .responsive-table .ant-empty {
    margin: 20px 0;
  }
  
  .responsive-table .ant-empty-description {
    font-size: 12px;
  }
}

/* 表格滚动条样式 */
.responsive-table::-webkit-scrollbar {
  height: 6px;
}

.responsive-table::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.responsive-table::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.responsive-table::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格边框优化 */
.responsive-table .ant-table-bordered .ant-table-thead > tr > th,
.responsive-table .ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .responsive-table .ant-table-bordered .ant-table-thead > tr > th,
  .responsive-table .ant-table-bordered .ant-table-tbody > tr > td {
    border-right: none;
  }
}

/* 表格行选择样式 */
.responsive-table .ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

/* 表格展开行样式 */
.responsive-table .ant-table-expanded-row > td {
  background-color: #fafafa;
}

@media (max-width: 768px) {
  .responsive-table .ant-table-expanded-row > td {
    padding: 8px;
  }
}

/* 表格固定列样式 */
.responsive-table .ant-table-fixed-left,
.responsive-table .ant-table-fixed-right {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .responsive-table .ant-table-fixed-left,
  .responsive-table .ant-table-fixed-right {
    box-shadow: none;
  }
}

/* 表格排序样式 */
.responsive-table .ant-table-column-sorter {
  color: #bfbfbf;
}

.responsive-table .ant-table-column-sorter-up.active,
.responsive-table .ant-table-column-sorter-down.active {
  color: #1890ff;
}

/* 表格筛选样式 */
.responsive-table .ant-table-filter-trigger {
  color: #bfbfbf;
}

.responsive-table .ant-table-filter-trigger.active {
  color: #1890ff;
}
