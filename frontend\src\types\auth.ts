// API标准响应格式
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
  rememberMe: boolean
}

// 登录响应原始数据
export interface LoginResponseData {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: UserProfile
}

// 登录响应（带标准格式）
export type LoginResponse = ApiResponse<LoginResponseData>

// 用户档案
export interface UserProfile {
  id: string
  user_name: string
  employee_id?: string
  email?: string
  phone_number?: string
  job_title?: string
  is_active: boolean
  department?: DepartmentSimple
  roles?: RoleSimple[]
  permissions?: string[]
}

// 简单部门信息
export interface DepartmentSimple {
  id: string
  name: string
  code: string
}

// 简单角色信息
export interface RoleSimple {
  id: string
  name: string
  code: string
}

// 简单部门信息
export interface DepartmentSimple {
  id: string
  name: string
  code: string
}

// 简单角色信息
export interface RoleSimple {
  id: string
  name: string
  code: string
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  fullName: string
  employeeId: string
  departmentId: number
  departmentName: string
  title: string
  roles: string[]
  createdAt: string
  updatedAt: string
}

// 部门信息
export interface Department {
  id: number
  name: string
  code: string
  parentId?: number
  level: number
  sort: number
}