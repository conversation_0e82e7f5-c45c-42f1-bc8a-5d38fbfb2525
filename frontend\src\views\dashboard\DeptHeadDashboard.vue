<template>
  <div class="dept-head-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>部门负责人工作台</h2>
      <p>{{ userStore.userDepartment }} - {{ userStore.userName }}</p>
    </div>

    <!-- 数据概览卡片 -->
    <a-row :gutter="[16, 16]" class="overview-cards">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="待我审批"
            :value="pendingApprovals.length"
            :value-style="{ color: '#cf1322' }"
            suffix="项"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="本月预算使用率"
            :value="budgetUsageRate"
            :precision="1"
            suffix="%"
            :value-style="{ color: budgetUsageRate > 80 ? '#cf1322' : '#3f8600' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="可用预算"
            :value="budgetOverview.availableBudget"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#3f8600' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="冻结金额"
            :value="budgetOverview.frozenBudget"
            :precision="2"
            prefix="¥"
            :value-style="{ color: '#faad14' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" class="main-content">
      <!-- 待我审批列表 -->
      <a-col :xs="24" :lg="14">
        <a-card title="待我审批" class="approval-card">
          <template #extra>
            <a-button type="link" @click="goToApprovalCenter">查看全部</a-button>
          </template>
          
          <a-list
            :data-source="pendingApprovals"
            :loading="approvalsLoading"
            item-layout="horizontal"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button type="primary" size="small" @click="handleApproval(item)">
                    处理
                  </a-button>
                </template>
                
                <a-list-item-meta>
                  <template #title>
                    <a @click="handleApproval(item)">{{ item.summary }}</a>
                  </template>
                  <template #description>
                    <div class="approval-meta">
                      <a-tag :color="getApprovalTypeColor(item.type)">{{ item.type }}</a-tag>
                      <span>申请人：{{ item.applicant }}</span>
                      <span v-if="item.amount">金额：¥{{ item.amount.toLocaleString() }}</span>
                      <span>提交时间：{{ formatDateTime(item.submissionTime) }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
            
            <template #loadMore>
              <div v-if="pendingApprovals.length === 0" class="empty-state">
                <a-empty description="暂无待审批事项" />
              </div>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <!-- 预算概览仪表盘 -->
      <a-col :xs="24" :lg="10">
        <a-card title="预算概览" class="budget-card">
          <div class="budget-chart" ref="budgetChartRef"></div>
          
          <div class="budget-details">
            <div class="budget-item">
              <span class="label">年度总预算：</span>
              <span class="value">¥{{ budgetOverview.totalBudget.toLocaleString() }}</span>
            </div>
            <div class="budget-item">
              <span class="label">已使用：</span>
              <span class="value used">¥{{ budgetOverview.usedBudget.toLocaleString() }}</span>
            </div>
            <div class="budget-item">
              <span class="label">冻结金额：</span>
              <span class="value frozen">¥{{ budgetOverview.frozenBudget.toLocaleString() }}</span>
            </div>
            <div class="budget-item">
              <span class="label">可用余额：</span>
              <span class="value available">¥{{ budgetOverview.availableBudget.toLocaleString() }}</span>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 快速操作 -->
    <a-row :gutter="[16, 16]" class="quick-actions">
      <a-col :span="24">
        <a-card title="快速操作">
          <div class="action-buttons">
            <a-button type="primary" @click="goToExpenseClaim">
              <PlusOutlined />
              新建报销申请
            </a-button>
            <a-button @click="goToPreApproval">
              <FileTextOutlined />
              事前申请
            </a-button>
            <a-button @click="goToBudgetQuery">
              <DollarOutlined />
              预算查询
            </a-button>
            <a-button @click="goToApprovalCenter">
              <AuditOutlined />
              审批中心
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { 
  PlusOutlined, 
  FileTextOutlined, 
  DollarOutlined, 
  AuditOutlined 
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { Chart } from '@antv/g2'
import { getDeptHeadDashboard } from '@/api/dashboard'
import type { BudgetOverviewResponse, ApprovalItemResponse } from '@/types/dashboard'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const approvalsLoading = ref(true)
const budgetChartRef = ref<HTMLElement | null>(null)
let budgetChart: Chart | null = null

// 待审批列表数据
const pendingApprovals = ref<ApprovalItemResponse[]>([])

// 预算概览数据
const budgetOverview = ref<BudgetOverviewResponse>({
  totalBudget: 0,
  usedBudget: 0,
  frozenBudget: 0,
  availableBudget: 0
})

// 计算预算使用率
const budgetUsageRate = computed(() => {
  const { totalBudget, usedBudget, frozenBudget } = budgetOverview.value
  return ((usedBudget + frozenBudget) / totalBudget) * 100
})

// 获取审批类型颜色
const getApprovalTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    '报销申请': 'blue',
    '合同审批': 'green',
    '事前申请': 'orange',
    '采购申请': 'purple'
  }
  return colorMap[type] || 'default'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('MM-DD HH:mm')
}

// 处理审批
const handleApproval = (item: any) => {
  router.push(item.link)
}

// 快速操作导航
const goToExpenseClaim = () => {
  router.push('/expense/applications')
}

const goToPreApproval = () => {
  router.push('/expense/pre-applications')
}

const goToBudgetQuery = () => {
  router.push('/budget/items')
}

const goToApprovalCenter = () => {
  router.push('/expense/approvals')
}

// 初始化预算图表
const initBudgetChart = async () => {
  await nextTick()
  if (!budgetChartRef.value) return

  if (budgetChart) {
    budgetChart.destroy()
  }
  
  try {
    const { totalBudget, usedBudget, frozenBudget, availableBudget } = budgetOverview.value
    
    // 准备数据 - 按照G2官网示例格式
    const data = [
      { 
        item: '已使用', 
        count: usedBudget,
        percent: totalBudget > 0 ? usedBudget / totalBudget : 0
      },
      { 
        item: '已冻结', 
        count: frozenBudget,
        percent: totalBudget > 0 ? frozenBudget / totalBudget : 0
      },
      { 
        item: '可用余额', 
        count: availableBudget,
        percent: totalBudget > 0 ? availableBudget / totalBudget : 0
      }
    ]
    
    // 根据G2官网最新API创建图表
    budgetChart = new Chart({
      container: budgetChartRef.value,
      autoFit: true,
      height: 300,
    })

    // 设置坐标系为极坐标（饼图）
    budgetChart.coordinate({ type: 'theta', outerRadius: 0.8 })

    // 添加饼图几何标记 - 使用最新API
    budgetChart
      .interval()
      .data(data)
      .transform({ type: 'stackY' })
      .encode('y', 'percent')
      .encode('color', 'item')
      .scale('color', {
        range: ['#1890ff', '#faad14', '#52c41a']
      })
      .legend('color', { 
        position: 'bottom', 
        layout: { justifyContent: 'center' } 
      })
      .label({
        position: 'outside',
        text: (data) => `${data.item}: ${(data.percent * 100).toFixed(1)}%`,
        style: {
          fontSize: 12,
          fill: '#666'
        }
      })
      .tooltip((data) => ({
        name: data.item,
        value: `¥${data.count.toLocaleString()} (${(data.percent * 100).toFixed(1)}%)`,
      }))
      .style({
        stroke: '#fff',
        lineWidth: 1,
      })
    
    // 渲染图表
    budgetChart.render()
    
    // 响应窗口大小变化
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('图表初始化失败:', error)
    // 如果图表初始化失败，显示简单的文字统计
    if (budgetChartRef.value) {
      const { totalBudget, usedBudget, frozenBudget, availableBudget } = budgetOverview.value
      const usedPercent = totalBudget > 0 ? ((usedBudget / totalBudget) * 100).toFixed(1) : '0.0'
      const frozenPercent = totalBudget > 0 ? ((frozenBudget / totalBudget) * 100).toFixed(1) : '0.0'
      const availablePercent = totalBudget > 0 ? ((availableBudget / totalBudget) * 100).toFixed(1) : '0.0'
      
      budgetChartRef.value.innerHTML = `
        <div style="padding: 20px; text-align: center;">
          <div style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">预算使用情况</div>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="color: #1890ff;">● 已使用</span>
              <span>${usedPercent}%</span>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="color: #faad14;">● 已冻结</span>
              <span>${frozenPercent}%</span>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="color: #52c41a;">● 可用余额</span>
              <span>${availablePercent}%</span>
            </div>
          </div>
        </div>
      `
    }
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (budgetChart) {
    budgetChart.forceFit()
  }
}

// 加载数据
const loadData = async () => {
  try {
    approvalsLoading.value = true
    
    // 调用API获取部门负责人工作台数据
    const response = await getDeptHeadDashboard()
    
    // 更新预算概览数据
    if (response.budgetOverview) {
      budgetOverview.value = response.budgetOverview
    }
    
    // 更新待审批列表数据
    if (response.pendingApprovals) {
      pendingApprovals.value = response.pendingApprovals
    }
    
  } catch (error) {
    console.error('数据加载失败:', error)
    message.error('数据加载失败')
  } finally {
    approvalsLoading.value = false
  }
}

// 生命周期钩子
onMounted(async () => {
  await loadData()
  await initBudgetChart()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (budgetChart) {
    budgetChart.destroy()
  }
})
</script>

<style scoped>
.dept-head-dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-cards .ant-card {
  height: 120px;
}

.overview-cards :deep(.ant-card-body) {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.main-content {
  margin-bottom: 24px;
}

.approval-card,
.budget-card {
  height: 500px;
}

.quick-actions .ant-card {
  min-height: 100px;
}

.quick-actions :deep(.ant-card-body) {
  padding: 20px;
  display: flex;
  align-items: center;
}

.approval-card :deep(.ant-card-body) {
  padding: 16px;
  height: calc(100% - 57px);
  overflow-y: auto;
}

.budget-card :deep(.ant-card-body) {
  display: flex;
  flex-direction: column;
  height: calc(100% - 57px);
  padding: 16px;
  overflow: hidden;
}

.approval-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #8c8c8c;
}

.approval-meta .ant-tag {
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.budget-chart {
  height: 240px;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.budget-details {
  flex-shrink: 0;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  margin-top: auto;
}

.budget-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.budget-item:last-child {
  border-bottom: none;
}

.budget-item .label {
  color: #8c8c8c;
  font-size: 13px;
  white-space: nowrap;
}

.budget-item .value {
  font-weight: 500;
  font-size: 13px;
  text-align: right;
}

.budget-item .value.used {
  color: #1890ff;
}

.budget-item .value.frozen {
  color: #faad14;
}

.budget-item .value.available {
  color: #52c41a;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action-buttons .ant-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 0 0 auto;
  min-width: 120px;
  justify-content: center;
}

@media (max-width: 768px) {
  .approval-meta {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .ant-btn {
    justify-content: center;
  }
}
</style>