import request from '@/utils/request'

/**
 * 支出控制相关API
 */
export const expenseApi = {
  /**
   * 创建报销申请
   * @param {Object} data 报销申请数据
   */
  createClaim(data) {
    return request({
      url: '/api/v1/expense-claims',
      method: 'post',
      data
    })
  },

  /**
   * 保存报销草稿
   * @param {Object} data 草稿数据
   */
  saveDraft(data) {
    return request({
      url: '/api/v1/expense-claims/draft',
      method: 'post',
      data
    })
  },

  /**
   * 获取报销申请详情
   * @param {number} id 报销申请ID
   */
  getClaim(id) {
    return request({
      url: `/api/v1/expense-claims/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取费用类型列表
   */
  getExpenseTypes() {
    return request({
      url: '/api/v1/expense-types',
      method: 'get'
    })
  },

  /**
   * 获取预算科目列表
   */
  getBudgetItems() {
    return request({
      url: '/api/v1/budget-items',
      method: 'get'
    })
  },

  /**
   * 获取预算余额
   * @param {number} budgetItemId 预算科目ID
   */
  getBudgetBalance(budgetItemId) {
    return request({
      url: `/api/v1/budget-items/${budgetItemId}/balance`,
      method: 'get'
    })
  },

  /**
   * 搜索事前申请单
   * @param {string} keyword 搜索关键词
   */
  searchPreApprovals(keyword) {
    return request({
      url: '/api/v1/pre-approvals/search',
      method: 'get',
      params: { keyword }
    })
  },

  /**
   * 搜索合同
   * @param {string} keyword 搜索关键词
   */
  searchContracts(keyword) {
    return request({
      url: '/api/v1/contracts/search',
      method: 'get',
      params: { keyword }
    })
  },

  /**
   * 获取用户报销申请列表
   * @param {Object} params 查询参数
   */
  getClaimList(params) {
    return request({
      url: '/api/v1/expense-claims',
      method: 'get',
      params
    })
  },

  /**
   * 删除报销申请
   * @param {number} id 报销申请ID
   */
  deleteClaim(id) {
    return request({
      url: `/api/v1/expense-claims/${id}`,
      method: 'delete'
    })
  },

  /**
   * 撤回报销申请
   * @param {number} id 报销申请ID
   */
  withdrawClaim(id) {
    return request({
      url: `/api/v1/expense-claims/${id}/withdraw`,
      method: 'post'
    })
  },

  /**
   * 获取部门预算概览
   * @param {number} departmentId 部门ID
   */
  getDepartmentBudgetOverview(departmentId) {
    return request({
      url: `/api/v1/departments/${departmentId}/budget-overview`,
      method: 'get'
    })
  },

  /**
   * 获取预算执行情况
   * @param {Object} params 查询参数
   */
  getBudgetExecution(params) {
    return request({
      url: '/api/v1/budget-execution',
      method: 'get',
      params
    })
  },

  /**
   * 导出报销申请
   * @param {Object} params 导出参数
   */
  exportClaims(params) {
    return request({
      url: '/api/v1/expense-claims/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 批量审批
   * @param {Object} data 批量审批数据
   */
  batchApproval(data) {
    return request({
      url: '/api/v1/expense-claims/batch-approval',
      method: 'post',
      data
    })
  },

  /**
   * 获取报销统计数据
   * @param {Object} params 查询参数
   */
  getClaimStatistics(params) {
    return request({
      url: '/api/v1/expense-claims/statistics',
      method: 'get',
      params
    })
  },

  /**
   * 获取发票OCR识别结果
   * @param {File} file 发票图片文件
   */
  recognizeInvoice(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/api/v1/invoice/ocr',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 验证发票真伪
   * @param {Object} invoiceInfo 发票信息
   */
  verifyInvoice(invoiceInfo) {
    return request({
      url: '/api/v1/invoice/verify',
      method: 'post',
      data: invoiceInfo
    })
  }
}

export default expenseApi
