import request from '@/utils/request'

/**
 * 用户相关API
 */
export const userApi = {
  /**
   * 用户登录
   * @param {Object} credentials 登录凭据
   */
  login(credentials) {
    return request({
      url: '/api/v1/login',
      method: 'post',
      data: credentials
    })
  },

  /**
   * 用户登出
   */
  logout() {
    return request({
      url: '/api/v1/logout',
      method: 'post'
    })
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return request({
      url: '/api/v1/user/info',
      method: 'get'
    })
  },

  /**
   * 更新用户信息
   * @param {Object} data 用户信息
   */
  updateUserInfo(data) {
    return request({
      url: '/api/v1/user/info',
      method: 'put',
      data
    })
  },

  /**
   * 修改密码
   * @param {Object} data 密码数据
   */
  changePassword(data) {
    return request({
      url: '/api/v1/user/password',
      method: 'put',
      data
    })
  },

  /**
   * 获取个人银行账户
   */
  getPersonalAccounts() {
    return request({
      url: '/api/v1/user/personal-accounts',
      method: 'get'
    })
  },

  /**
   * 添加个人银行账户
   * @param {Object} accountData 账户数据
   */
  addPersonalAccount(accountData) {
    return request({
      url: '/api/v1/user/personal-accounts',
      method: 'post',
      data: accountData
    })
  },

  /**
   * 删除个人银行账户
   * @param {number} accountId 账户ID
   */
  deletePersonalAccount(accountId) {
    return request({
      url: `/api/v1/user/personal-accounts/${accountId}`,
      method: 'delete'
    })
  },

  /**
   * 刷新token
   */
  refreshToken() {
    return request({
      url: '/api/v1/auth/refresh',
      method: 'post'
    })
  },

  /**
   * 获取用户权限
   */
  getUserPermissions() {
    return request({
      url: '/api/v1/user/permissions',
      method: 'get'
    })
  },

  /**
   * 获取用户角色
   */
  getUserRoles() {
    return request({
      url: '/api/v1/user/roles',
      method: 'get'
    })
  }
}

export default userApi
