import request from '@/utils/request'

/**
 * 工作流相关API
 */
export const workflowApi = {
  /**
   * 获取审批流程预览
   * @param {Object} params 预览参数
   */
  getWorkflowPreview(params) {
    return request({
      url: '/api/v1/workflow/preview',
      method: 'get',
      params
    })
  },

  /**
   * 获取流程实例详情
   * @param {number} instanceId 流程实例ID
   */
  getWorkflowInstance(instanceId) {
    return request({
      url: `/api/v1/workflow/instance/${instanceId}`,
      method: 'get'
    })
  },

  /**
   * 提交审批决定
   * @param {Object} data 审批数据
   */
  submitApproval(data) {
    return request({
      url: '/api/v1/approvals',
      method: 'post',
      data
    })
  },

  /**
   * 获取审批历史
   * @param {number} instanceId 流程实例ID
   */
  getApprovalHistory(instanceId) {
    return request({
      url: `/api/v1/workflow/instance/${instanceId}/history`,
      method: 'get'
    })
  },

  /**
   * 获取待办任务列表
   * @param {Object} params 查询参数
   */
  getPendingTasks(params) {
    return request({
      url: '/api/v1/workflow/pending-tasks',
      method: 'get',
      params
    })
  },

  /**
   * 获取已办任务列表
   * @param {Object} params 查询参数
   */
  getCompletedTasks(params) {
    return request({
      url: '/api/v1/workflow/completed-tasks',
      method: 'get',
      params
    })
  },

  /**
   * 委托审批
   * @param {Object} data 委托数据
   */
  delegateApproval(data) {
    return request({
      url: '/api/v1/workflow/delegate',
      method: 'post',
      data
    })
  },

  /**
   * 转办审批
   * @param {Object} data 转办数据
   */
  transferApproval(data) {
    return request({
      url: '/api/v1/workflow/transfer',
      method: 'post',
      data
    })
  },

  /**
   * 加签审批
   * @param {Object} data 加签数据
   */
  addApprover(data) {
    return request({
      url: '/api/v1/workflow/add-approver',
      method: 'post',
      data
    })
  },

  /**
   * 获取流程定义列表
   */
  getWorkflowDefinitions() {
    return request({
      url: '/api/v1/workflow/definitions',
      method: 'get'
    })
  },

  /**
   * 启动流程实例
   * @param {Object} data 启动数据
   */
  startWorkflow(data) {
    return request({
      url: '/api/v1/workflow/start',
      method: 'post',
      data
    })
  },

  /**
   * 终止流程实例
   * @param {number} instanceId 流程实例ID
   * @param {string} reason 终止原因
   */
  terminateWorkflow(instanceId, reason) {
    return request({
      url: `/api/v1/workflow/instance/${instanceId}/terminate`,
      method: 'post',
      data: { reason }
    })
  },

  /**
   * 获取流程统计数据
   * @param {Object} params 查询参数
   */
  getWorkflowStatistics(params) {
    return request({
      url: '/api/v1/workflow/statistics',
      method: 'get',
      params
    })
  },

  /**
   * 获取审批人候选列表
   * @param {Object} params 查询参数
   */
  getApproverCandidates(params) {
    return request({
      url: '/api/v1/workflow/approver-candidates',
      method: 'get',
      params
    })
  },

  /**
   * 批量审批
   * @param {Object} data 批量审批数据
   */
  batchApproval(data) {
    return request({
      url: '/api/v1/workflow/batch-approval',
      method: 'post',
      data
    })
  },

  /**
   * 获取流程图数据
   * @param {string} definitionKey 流程定义Key
   */
  getWorkflowDiagram(definitionKey) {
    return request({
      url: `/api/v1/workflow/diagram/${definitionKey}`,
      method: 'get'
    })
  },

  /**
   * 获取节点配置
   * @param {string} definitionKey 流程定义Key
   * @param {string} nodeId 节点ID
   */
  getNodeConfig(definitionKey, nodeId) {
    return request({
      url: `/api/v1/workflow/definitions/${definitionKey}/nodes/${nodeId}`,
      method: 'get'
    })
  },

  /**
   * 获取我发起的流程
   * @param {Object} params 查询参数
   */
  getMyStartedWorkflows(params) {
    return request({
      url: '/api/v1/workflow/my-started',
      method: 'get',
      params
    })
  },

  /**
   * 获取抄送给我的流程
   * @param {Object} params 查询参数
   */
  getCcWorkflows(params) {
    return request({
      url: '/api/v1/workflow/cc-to-me',
      method: 'get',
      params
    })
  },

  /**
   * 催办
   * @param {number} instanceId 流程实例ID
   * @param {string} message 催办消息
   */
  urgeApproval(instanceId, message) {
    return request({
      url: `/api/v1/workflow/instance/${instanceId}/urge`,
      method: 'post',
      data: { message }
    })
  }
}

export default workflowApi
