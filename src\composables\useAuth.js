import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'

/**
 * 认证相关的组合式函数
 */
export function useAuth() {
  const router = useRouter()
  const userStore = useUserStore()
  
  const loading = ref(false)
  
  // 计算属性
  const isLoggedIn = computed(() => userStore.isLoggedIn)
  const userInfo = computed(() => userStore.userInfo)
  const hasPermission = computed(() => userStore.hasPermission)
  const hasRole = computed(() => userStore.hasRole)
  
  /**
   * 初始化认证状态
   */
  const initAuth = async () => {
    try {
      loading.value = true
      
      // 从localStorage恢复用户状态
      userStore.initUserState()
      
      // 如果有token，验证token有效性并获取最新用户信息
      if (userStore.token) {
        try {
          await userStore.fetchUserInfo()
          await userStore.loadPersonalAccounts()
        } catch (error) {
          console.error('验证token失败:', error)
          // token无效，清除登录状态
          await userStore.logout()
          return false
        }
      }
      
      return userStore.isLoggedIn
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 登录
   */
  const login = async (credentials) => {
    try {
      loading.value = true
      
      const response = await userStore.login(credentials)
      
      // 获取用户详细信息
      await userStore.fetchUserInfo()
      await userStore.loadPersonalAccounts()
      
      message.success('登录成功')
      return response
    } catch (error) {
      console.error('登录失败:', error)
      
      // 处理不同的错误状态
      if (error.response?.status === 401) {
        message.error('用户名或密码错误')
      } else if (error.response?.status === 423) {
        message.error('账户已被锁定，请联系管理员')
      } else if (error.response?.status === 403) {
        message.error('账户已被禁用，请联系管理员')
      } else {
        message.error(error.message || '登录失败，请稍后重试')
      }
      
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 登出
   */
  const logout = async () => {
    try {
      await userStore.logout()
      message.success('已安全退出')
      router.push('/login')
    } catch (error) {
      console.error('登出失败:', error)
      // 即使登出失败也要清除本地状态
      userStore.resetState()
      router.push('/login')
    }
  }
  
  /**
   * 检查权限
   */
  const checkPermission = (permission) => {
    return userStore.checkPermission(permission)
  }
  
  /**
   * 检查角色
   */
  const checkRole = (role) => {
    return userStore.checkRole(role)
  }
  
  /**
   * 检查是否需要登录
   */
  const requireAuth = () => {
    if (!userStore.isLoggedIn) {
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      })
      return false
    }
    return true
  }
  
  /**
   * 检查权限并跳转
   */
  const requirePermission = (permission) => {
    if (!requireAuth()) {
      return false
    }
    
    if (!checkPermission(permission)) {
      message.error('您没有权限访问此功能')
      router.push('/403')
      return false
    }
    
    return true
  }
  
  /**
   * 检查角色并跳转
   */
  const requireRole = (role) => {
    if (!requireAuth()) {
      return false
    }
    
    if (!checkRole(role)) {
      message.error('您没有权限访问此功能')
      router.push('/403')
      return false
    }
    
    return true
  }
  
  /**
   * 刷新用户信息
   */
  const refreshUserInfo = async () => {
    try {
      await userStore.fetchUserInfo()
      await userStore.loadPersonalAccounts()
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      throw error
    }
  }
  
  /**
   * 更新用户信息
   */
  const updateUserInfo = async (data) => {
    try {
      const response = await userStore.updateUserInfo(data)
      message.success('用户信息更新成功')
      return response
    } catch (error) {
      console.error('更新用户信息失败:', error)
      message.error('更新用户信息失败')
      throw error
    }
  }
  
  /**
   * 修改密码
   */
  const changePassword = async (data) => {
    try {
      const response = await userStore.changePassword(data)
      message.success('密码修改成功，请重新登录')
      
      // 修改密码后需要重新登录
      await logout()
      
      return response
    } catch (error) {
      console.error('修改密码失败:', error)
      message.error('修改密码失败')
      throw error
    }
  }
  
  return {
    // 状态
    loading,
    
    // 计算属性
    isLoggedIn,
    userInfo,
    hasPermission,
    hasRole,
    
    // 方法
    initAuth,
    login,
    logout,
    checkPermission,
    checkRole,
    requireAuth,
    requirePermission,
    requireRole,
    refreshUserInfo,
    updateUserInfo,
    changePassword
  }
}

export default useAuth
