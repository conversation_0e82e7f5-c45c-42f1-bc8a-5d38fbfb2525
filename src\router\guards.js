import { useUserStore } from '@/stores/user'
import { message } from 'ant-design-vue'

/**
 * 路由守卫配置
 */

// 不需要登录的页面
const whiteList = ['/login', '/404', '/403', '/500']

// 权限路由映射
const permissionRoutes = {
  '/expense': ['expense:view'],
  '/expense/claim': ['expense:claim:view'],
  '/expense/claim/new': ['expense:claim:create'],
  '/expense/claim/edit': ['expense:claim:edit'],
  '/workflow/approval': ['workflow:approval'],
  '/workflow/pending': ['workflow:pending:view'],
  '/workflow/completed': ['workflow:completed:view'],
  '/dashboard': ['dashboard:view'],
  '/settings': ['settings:view']
}

// 角色路由映射
const roleRoutes = {
  '/admin': ['admin'],
  '/finance': ['finance_staff', 'finance_manager'],
  '/dept-head': ['dept_head']
}

/**
 * 前置守卫
 */
export function beforeEach(to, from, next) {
  const userStore = useUserStore()
  
  // 初始化用户状态
  if (!userStore.userInfo.id && userStore.token) {
    userStore.initUserState()
  }
  
  // 检查是否在白名单中
  if (whiteList.includes(to.path)) {
    next()
    return
  }
  
  // 检查是否已登录
  if (!userStore.isLoggedIn) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 检查权限
  if (!checkRoutePermission(to.path, userStore)) {
    message.error('您没有权限访问此页面')
    next('/403')
    return
  }
  
  // 检查角色
  if (!checkRouteRole(to.path, userStore)) {
    message.error('您没有权限访问此页面')
    next('/403')
    return
  }
  
  next()
}

/**
 * 后置守卫
 */
export function afterEach(to, from) {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 寻甸县第一人民医院运营管理系统`
  } else {
    document.title = '寻甸县第一人民医院运营管理系统'
  }
  
  // 记录页面访问日志
  if (process.env.NODE_ENV === 'production') {
    console.log(`页面访问: ${from.path} -> ${to.path}`)
  }
}

/**
 * 检查路由权限
 */
function checkRoutePermission(path, userStore) {
  // 查找匹配的权限规则
  for (const [routePath, permissions] of Object.entries(permissionRoutes)) {
    if (path.startsWith(routePath)) {
      // 检查用户是否有任一所需权限
      return permissions.some(permission => 
        userStore.checkPermission(permission)
      )
    }
  }
  
  // 如果没有配置权限规则，默认允许访问
  return true
}

/**
 * 检查路由角色
 */
function checkRouteRole(path, userStore) {
  // 查找匹配的角色规则
  for (const [routePath, roles] of Object.entries(roleRoutes)) {
    if (path.startsWith(routePath)) {
      // 检查用户是否有任一所需角色
      return roles.some(role => 
        userStore.checkRole(role)
      )
    }
  }
  
  // 如果没有配置角色规则，默认允许访问
  return true
}

/**
 * 动态添加权限路由
 */
export function addPermissionRoute(path, permissions) {
  permissionRoutes[path] = permissions
}

/**
 * 动态添加角色路由
 */
export function addRoleRoute(path, roles) {
  roleRoutes[path] = roles
}

/**
 * 移除权限路由
 */
export function removePermissionRoute(path) {
  delete permissionRoutes[path]
}

/**
 * 移除角色路由
 */
export function removeRoleRoute(path) {
  delete roleRoutes[path]
}

/**
 * 获取所有权限路由
 */
export function getPermissionRoutes() {
  return { ...permissionRoutes }
}

/**
 * 获取所有角色路由
 */
export function getRoleRoutes() {
  return { ...roleRoutes }
}

/**
 * 检查用户是否可以访问指定路径
 */
export function canAccessRoute(path, userStore) {
  // 检查白名单
  if (whiteList.includes(path)) {
    return true
  }
  
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    return false
  }
  
  // 检查权限和角色
  return checkRoutePermission(path, userStore) && checkRouteRole(path, userStore)
}

export default {
  beforeEach,
  afterEach,
  addPermissionRoute,
  addRoleRoute,
  removePermissionRoute,
  removeRoleRoute,
  getPermissionRoutes,
  getRoleRoutes,
  canAccessRoute
}
