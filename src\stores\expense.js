import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { expenseApi } from '@/api/expense'

/**
 * 支出控制管理Store
 */
export const useExpenseStore = defineStore('expense', () => {
  // 状态数据
  const claimList = ref([])
  const currentClaim = ref({})
  const expenseTypes = ref([])
  const budgetItems = ref([])
  const loading = ref(false)
  const submitting = ref(false)
  
  // 分页信息
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })
  
  // 查询条件
  const searchParams = ref({
    status: '',
    dateRange: [],
    keyword: '',
    departmentId: null
  })
  
  // 统计数据
  const statistics = ref({
    totalAmount: 0,
    pendingCount: 0,
    approvedCount: 0,
    rejectedCount: 0
  })

  // 计算属性
  const pendingClaims = computed(() => {
    return claimList.value.filter(claim => claim.status === 'pending')
  })
  
  const approvedClaims = computed(() => {
    return claimList.value.filter(claim => claim.status === 'approved')
  })
  
  const rejectedClaims = computed(() => {
    return claimList.value.filter(claim => claim.status === 'rejected')
  })
  
  const draftClaims = computed(() => {
    return claimList.value.filter(claim => claim.status === 'draft')
  })

  // Actions
  
  /**
   * 获取报销申请列表
   */
  const fetchClaimList = async (params = {}) => {
    try {
      loading.value = true
      
      const queryParams = {
        ...searchParams.value,
        ...params,
        page: pagination.value.current,
        pageSize: pagination.value.pageSize
      }
      
      const response = await expenseApi.getClaimList(queryParams)
      
      claimList.value = response.data
      pagination.value.total = response.total
      
      return response
    } catch (error) {
      console.error('获取报销申请列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取报销申请详情
   */
  const fetchClaimDetail = async (id) => {
    try {
      loading.value = true
      const response = await expenseApi.getClaim(id)
      currentClaim.value = response
      return response
    } catch (error) {
      console.error('获取报销申请详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 创建报销申请
   */
  const createClaim = async (data) => {
    try {
      submitting.value = true
      const response = await expenseApi.createClaim(data)
      
      // 刷新列表
      await fetchClaimList()
      
      return response
    } catch (error) {
      console.error('创建报销申请失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 保存草稿
   */
  const saveDraft = async (data) => {
    try {
      submitting.value = true
      const response = await expenseApi.saveDraft(data)
      
      // 刷新列表
      await fetchClaimList()
      
      return response
    } catch (error) {
      console.error('保存草稿失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 删除报销申请
   */
  const deleteClaim = async (id) => {
    try {
      await expenseApi.deleteClaim(id)
      
      // 从列表中移除
      const index = claimList.value.findIndex(claim => claim.id === id)
      if (index > -1) {
        claimList.value.splice(index, 1)
      }
      
      // 更新统计
      await fetchStatistics()
      
    } catch (error) {
      console.error('删除报销申请失败:', error)
      throw error
    }
  }
  
  /**
   * 撤回报销申请
   */
  const withdrawClaim = async (id) => {
    try {
      await expenseApi.withdrawClaim(id)
      
      // 更新列表中的状态
      const claim = claimList.value.find(item => item.id === id)
      if (claim) {
        claim.status = 'draft'
      }
      
      // 更新统计
      await fetchStatistics()
      
    } catch (error) {
      console.error('撤回报销申请失败:', error)
      throw error
    }
  }
  
  /**
   * 获取费用类型列表
   */
  const fetchExpenseTypes = async () => {
    try {
      const response = await expenseApi.getExpenseTypes()
      expenseTypes.value = response.data
      return response
    } catch (error) {
      console.error('获取费用类型失败:', error)
      throw error
    }
  }
  
  /**
   * 获取预算科目列表
   */
  const fetchBudgetItems = async () => {
    try {
      const response = await expenseApi.getBudgetItems()
      budgetItems.value = response.data
      return response
    } catch (error) {
      console.error('获取预算科目失败:', error)
      throw error
    }
  }
  
  /**
   * 获取统计数据
   */
  const fetchStatistics = async () => {
    try {
      const response = await expenseApi.getClaimStatistics()
      statistics.value = response
      return response
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  }
  
  /**
   * 设置搜索参数
   */
  const setSearchParams = (params) => {
    Object.assign(searchParams.value, params)
  }
  
  /**
   * 重置搜索参数
   */
  const resetSearchParams = () => {
    searchParams.value = {
      status: '',
      dateRange: [],
      keyword: '',
      departmentId: null
    }
  }
  
  /**
   * 设置分页参数
   */
  const setPagination = (page, pageSize) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
  }
  
  /**
   * 重置状态
   */
  const resetState = () => {
    claimList.value = []
    currentClaim.value = {}
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
    resetSearchParams()
  }
  
  /**
   * 导出报销申请
   */
  const exportClaims = async (params = {}) => {
    try {
      const response = await expenseApi.exportClaims({
        ...searchParams.value,
        ...params
      })
      
      // 创建下载链接
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `报销申请_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
    } catch (error) {
      console.error('导出报销申请失败:', error)
      throw error
    }
  }

  return {
    // 状态
    claimList,
    currentClaim,
    expenseTypes,
    budgetItems,
    loading,
    submitting,
    pagination,
    searchParams,
    statistics,
    
    // 计算属性
    pendingClaims,
    approvedClaims,
    rejectedClaims,
    draftClaims,
    
    // 方法
    fetchClaimList,
    fetchClaimDetail,
    createClaim,
    saveDraft,
    deleteClaim,
    withdrawClaim,
    fetchExpenseTypes,
    fetchBudgetItems,
    fetchStatistics,
    setSearchParams,
    resetSearchParams,
    setPagination,
    resetState,
    exportClaims
  }
})
