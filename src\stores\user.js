import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/user'

/**
 * 用户管理Store
 */
export const useUserStore = defineStore('user', () => {
  // 状态数据
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  const personalAccounts = ref([])
  const permissions = ref(JSON.parse(localStorage.getItem('permissions') || '[]'))
  const roles = ref(JSON.parse(localStorage.getItem('roles') || '[]'))
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!token.value && !!userInfo.value.id
  })
  
  const hasPermission = computed(() => {
    return (permission) => {
      return permissions.value.includes(permission)
    }
  })
  
  const hasRole = computed(() => {
    return (role) => {
      return roles.value.includes(role)
    }
  })
  
  const isDeptHead = computed(() => {
    return roles.value.includes('dept_head')
  })
  
  const isFinanceStaff = computed(() => {
    return roles.value.includes('finance_staff')
  })
  
  const isAdmin = computed(() => {
    return roles.value.includes('admin')
  })

  // Actions
  
  /**
   * 用户登录
   */
  const login = async (credentials) => {
    try {
      loading.value = true
      const response = await userApi.login(credentials)

      // 保存token
      token.value = response.token || response.access_token
      localStorage.setItem('token', response.token || response.access_token)

      // 保存用户信息
      userInfo.value = response.userInfo || response.user
      localStorage.setItem('userInfo', JSON.stringify(response.userInfo || response.user))

      // 保存权限和角色
      permissions.value = response.permissions || []
      roles.value = response.roles || []
      localStorage.setItem('permissions', JSON.stringify(response.permissions || []))
      localStorage.setItem('roles', JSON.stringify(response.roles || []))

      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 用户登录 (兼容现有登录页面)
   */
  const loginAction = async (credentials) => {
    return await login(credentials)
  }
  
  /**
   * 用户登出
   */
  const logout = async () => {
    try {
      await userApi.logout()
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = {}
      personalAccounts.value = []
      permissions.value = []
      roles.value = []

      // 清除localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('permissions')
      localStorage.removeItem('roles')
    }
  }
  
  /**
   * 获取用户信息
   */
  const fetchUserInfo = async () => {
    try {
      loading.value = true
      const response = await userApi.getUserInfo()

      // 更新用户信息
      userInfo.value = response.userInfo
      localStorage.setItem('userInfo', JSON.stringify(response.userInfo))

      // 更新权限和角色
      permissions.value = response.permissions || []
      roles.value = response.roles || []
      localStorage.setItem('permissions', JSON.stringify(response.permissions || []))
      localStorage.setItem('roles', JSON.stringify(response.roles || []))

      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 更新用户信息
   */
  const updateUserInfo = async (data) => {
    try {
      loading.value = true
      const response = await userApi.updateUserInfo(data)
      
      // 更新本地用户信息
      Object.assign(userInfo.value, response.userInfo)
      
      return response
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 修改密码
   */
  const changePassword = async (data) => {
    try {
      loading.value = true
      const response = await userApi.changePassword(data)
      return response
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 加载个人银行账户
   */
  const loadPersonalAccounts = async () => {
    try {
      const response = await userApi.getPersonalAccounts()
      personalAccounts.value = response.data
      return response
    } catch (error) {
      console.error('获取个人账户失败:', error)
      throw error
    }
  }
  
  /**
   * 添加个人银行账户
   */
  const addPersonalAccount = async (accountData) => {
    try {
      const response = await userApi.addPersonalAccount(accountData)
      
      // 重新加载账户列表
      await loadPersonalAccounts()
      
      return response
    } catch (error) {
      console.error('添加个人账户失败:', error)
      throw error
    }
  }
  
  /**
   * 删除个人银行账户
   */
  const deletePersonalAccount = async (accountId) => {
    try {
      await userApi.deletePersonalAccount(accountId)
      
      // 从列表中移除
      const index = personalAccounts.value.findIndex(account => account.id === accountId)
      if (index > -1) {
        personalAccounts.value.splice(index, 1)
      }
      
    } catch (error) {
      console.error('删除个人账户失败:', error)
      throw error
    }
  }
  
  /**
   * 刷新token
   */
  const refreshToken = async () => {
    try {
      const response = await userApi.refreshToken()
      
      token.value = response.token
      localStorage.setItem('token', response.token)
      
      return response
    } catch (error) {
      console.error('刷新token失败:', error)
      // token刷新失败，清除登录状态
      await logout()
      throw error
    }
  }
  
  /**
   * 检查权限
   */
  const checkPermission = (permission) => {
    return permissions.value.includes(permission)
  }
  
  /**
   * 检查角色
   */
  const checkRole = (role) => {
    return roles.value.includes(role)
  }
  
  /**
   * 重置状态
   */
  const resetState = () => {
    token.value = ''
    userInfo.value = {}
    personalAccounts.value = []
    permissions.value = []
    roles.value = []

    // 清除localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('permissions')
    localStorage.removeItem('roles')
  }

  /**
   * 初始化用户状态（从localStorage恢复）
   */
  const initUserState = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')
    const savedPermissions = localStorage.getItem('permissions')
    const savedRoles = localStorage.getItem('roles')

    if (savedToken) {
      token.value = savedToken
    }

    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        userInfo.value = {}
      }
    }

    if (savedPermissions) {
      try {
        permissions.value = JSON.parse(savedPermissions)
      } catch (error) {
        console.error('解析权限信息失败:', error)
        permissions.value = []
      }
    }

    if (savedRoles) {
      try {
        roles.value = JSON.parse(savedRoles)
      } catch (error) {
        console.error('解析角色信息失败:', error)
        roles.value = []
      }
    }
  }

  return {
    // 状态
    token,
    userInfo,
    personalAccounts,
    permissions,
    roles,
    loading,
    
    // 计算属性
    isLoggedIn,
    hasPermission,
    hasRole,
    isDeptHead,
    isFinanceStaff,
    isAdmin,
    
    // 方法
    login,
    loginAction,
    logout,
    fetchUserInfo,
    updateUserInfo,
    changePassword,
    loadPersonalAccounts,
    addPersonalAccount,
    deletePersonalAccount,
    refreshToken,
    checkPermission,
    checkRole,
    resetState,
    initUserState
  }
})
