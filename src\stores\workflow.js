import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { workflowApi } from '@/api/workflow'

/**
 * 工作流管理Store
 */
export const useWorkflowStore = defineStore('workflow', () => {
  // 状态数据
  const pendingTasks = ref([])
  const completedTasks = ref([])
  const myStartedWorkflows = ref([])
  const ccWorkflows = ref([])
  const currentInstance = ref({})
  const approvalHistory = ref([])
  const loading = ref(false)
  const submitting = ref(false)
  
  // 分页信息
  const pendingPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })
  
  const completedPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })
  
  // 查询条件
  const searchParams = ref({
    businessType: '',
    dateRange: [],
    keyword: '',
    status: ''
  })
  
  // 统计数据
  const statistics = ref({
    pendingCount: 0,
    todayCompleted: 0,
    weekCompleted: 0,
    monthCompleted: 0
  })

  // 计算属性
  const urgentTasks = computed(() => {
    return pendingTasks.value.filter(task => task.isUrgent)
  })
  
  const overdueeTasks = computed(() => {
    const now = new Date()
    return pendingTasks.value.filter(task => {
      return task.deadline && new Date(task.deadline) < now
    })
  })
  
  const todayTasks = computed(() => {
    const today = new Date().toDateString()
    return pendingTasks.value.filter(task => {
      return task.createdAt && new Date(task.createdAt).toDateString() === today
    })
  })

  // Actions
  
  /**
   * 获取待办任务列表
   */
  const fetchPendingTasks = async (params = {}) => {
    try {
      loading.value = true
      
      const queryParams = {
        ...searchParams.value,
        ...params,
        page: pendingPagination.value.current,
        pageSize: pendingPagination.value.pageSize
      }
      
      const response = await workflowApi.getPendingTasks(queryParams)
      
      pendingTasks.value = response.data
      pendingPagination.value.total = response.total
      
      return response
    } catch (error) {
      console.error('获取待办任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取已办任务列表
   */
  const fetchCompletedTasks = async (params = {}) => {
    try {
      loading.value = true
      
      const queryParams = {
        ...searchParams.value,
        ...params,
        page: completedPagination.value.current,
        pageSize: completedPagination.value.pageSize
      }
      
      const response = await workflowApi.getCompletedTasks(queryParams)
      
      completedTasks.value = response.data
      completedPagination.value.total = response.total
      
      return response
    } catch (error) {
      console.error('获取已办任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取流程实例详情
   */
  const fetchWorkflowInstance = async (instanceId) => {
    try {
      loading.value = true
      const response = await workflowApi.getWorkflowInstance(instanceId)
      currentInstance.value = response
      return response
    } catch (error) {
      console.error('获取流程实例失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取审批历史
   */
  const fetchApprovalHistory = async (instanceId) => {
    try {
      const response = await workflowApi.getApprovalHistory(instanceId)
      approvalHistory.value = response.data
      return response
    } catch (error) {
      console.error('获取审批历史失败:', error)
      throw error
    }
  }
  
  /**
   * 提交审批决定
   */
  const submitApproval = async (data) => {
    try {
      submitting.value = true
      const response = await workflowApi.submitApproval(data)
      
      // 刷新待办任务列表
      await fetchPendingTasks()
      
      // 更新统计数据
      await fetchStatistics()
      
      return response
    } catch (error) {
      console.error('提交审批失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 批量审批
   */
  const batchApproval = async (data) => {
    try {
      submitting.value = true
      const response = await workflowApi.batchApproval(data)
      
      // 刷新待办任务列表
      await fetchPendingTasks()
      
      // 更新统计数据
      await fetchStatistics()
      
      return response
    } catch (error) {
      console.error('批量审批失败:', error)
      throw error
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 委托审批
   */
  const delegateApproval = async (data) => {
    try {
      const response = await workflowApi.delegateApproval(data)
      
      // 刷新待办任务列表
      await fetchPendingTasks()
      
      return response
    } catch (error) {
      console.error('委托审批失败:', error)
      throw error
    }
  }
  
  /**
   * 转办审批
   */
  const transferApproval = async (data) => {
    try {
      const response = await workflowApi.transferApproval(data)
      
      // 刷新待办任务列表
      await fetchPendingTasks()
      
      return response
    } catch (error) {
      console.error('转办审批失败:', error)
      throw error
    }
  }
  
  /**
   * 获取我发起的流程
   */
  const fetchMyStartedWorkflows = async (params = {}) => {
    try {
      loading.value = true
      const response = await workflowApi.getMyStartedWorkflows(params)
      myStartedWorkflows.value = response.data
      return response
    } catch (error) {
      console.error('获取我发起的流程失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取抄送给我的流程
   */
  const fetchCcWorkflows = async (params = {}) => {
    try {
      loading.value = true
      const response = await workflowApi.getCcWorkflows(params)
      ccWorkflows.value = response.data
      return response
    } catch (error) {
      console.error('获取抄送流程失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取统计数据
   */
  const fetchStatistics = async () => {
    try {
      const response = await workflowApi.getWorkflowStatistics()
      statistics.value = response
      return response
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  }
  
  /**
   * 催办
   */
  const urgeApproval = async (instanceId, message) => {
    try {
      const response = await workflowApi.urgeApproval(instanceId, message)
      return response
    } catch (error) {
      console.error('催办失败:', error)
      throw error
    }
  }
  
  /**
   * 终止流程
   */
  const terminateWorkflow = async (instanceId, reason) => {
    try {
      const response = await workflowApi.terminateWorkflow(instanceId, reason)
      
      // 刷新相关列表
      await fetchMyStartedWorkflows()
      
      return response
    } catch (error) {
      console.error('终止流程失败:', error)
      throw error
    }
  }
  
  /**
   * 设置搜索参数
   */
  const setSearchParams = (params) => {
    Object.assign(searchParams.value, params)
  }
  
  /**
   * 重置搜索参数
   */
  const resetSearchParams = () => {
    searchParams.value = {
      businessType: '',
      dateRange: [],
      keyword: '',
      status: ''
    }
  }
  
  /**
   * 设置待办分页参数
   */
  const setPendingPagination = (page, pageSize) => {
    pendingPagination.value.current = page
    pendingPagination.value.pageSize = pageSize
  }
  
  /**
   * 设置已办分页参数
   */
  const setCompletedPagination = (page, pageSize) => {
    completedPagination.value.current = page
    completedPagination.value.pageSize = pageSize
  }
  
  /**
   * 重置状态
   */
  const resetState = () => {
    pendingTasks.value = []
    completedTasks.value = []
    myStartedWorkflows.value = []
    ccWorkflows.value = []
    currentInstance.value = {}
    approvalHistory.value = []
    
    pendingPagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
    
    completedPagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
    
    resetSearchParams()
  }

  return {
    // 状态
    pendingTasks,
    completedTasks,
    myStartedWorkflows,
    ccWorkflows,
    currentInstance,
    approvalHistory,
    loading,
    submitting,
    pendingPagination,
    completedPagination,
    searchParams,
    statistics,
    
    // 计算属性
    urgentTasks,
    overdueeTasks,
    todayTasks,
    
    // 方法
    fetchPendingTasks,
    fetchCompletedTasks,
    fetchWorkflowInstance,
    fetchApprovalHistory,
    submitApproval,
    batchApproval,
    delegateApproval,
    transferApproval,
    fetchMyStartedWorkflows,
    fetchCcWorkflows,
    fetchStatistics,
    urgeApproval,
    terminateWorkflow,
    setSearchParams,
    resetSearchParams,
    setPendingPagination,
    setCompletedPagination,
    resetState
  }
})
