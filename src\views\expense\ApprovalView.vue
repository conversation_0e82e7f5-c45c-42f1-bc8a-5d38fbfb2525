<template>
  <div class="approval-view">
    <a-row :gutter="24">
      <!-- 左侧：单据详情 -->
      <a-col :span="16">
        <a-card title="单据详情" :bordered="false">
          <component
            :is="detailComponent"
            :data="businessData"
            :readonly="true"
          />
        </a-card>
      </a-col>
      
      <!-- 右侧：审批操作与历史 -->
      <a-col :span="8">
        <!-- 流程图 -->
        <a-card title="审批流程" size="small" class="mb-4">
          <div ref="flowChartRef" class="flow-chart-container"></div>
        </a-card>
        
        <!-- 审批操作区 -->
        <a-card title="审批操作" size="small" class="mb-4">
          <a-form
            ref="approvalFormRef"
            :model="approvalForm"
            :rules="approvalRules"
            layout="vertical"
            @finish="handleApproval"
          >
            <a-form-item label="审批意见" name="comment">
              <a-textarea
                v-model:value="approvalForm.comment"
                :rows="4"
                placeholder="请输入审批意见"
              />
            </a-form-item>
            
            <a-form-item>
              <a-space>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="approving"
                  @click="approvalForm.action = 'approve'"
                >
                  同意
                </a-button>
                <a-button
                  type="danger"
                  :loading="approving"
                  @click="handleReject"
                >
                  驳回
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
        
        <!-- 审批历史 -->
        <a-card title="审批历史" size="small">
          <a-timeline>
            <a-timeline-item
              v-for="(item, index) in approvalHistory"
              :key="index"
              :color="getTimelineColor(item.action)"
            >
              <template #dot>
                <check-circle-outlined
                  v-if="item.action === 'approve'"
                  style="color: #52c41a"
                />
                <close-circle-outlined
                  v-else-if="item.action === 'reject'"
                  style="color: #ff4d4f"
                />
                <clock-circle-outlined
                  v-else
                  style="color: #1890ff"
                />
              </template>
              
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="node-name">{{ item.nodeName }}</span>
                  <span class="timestamp">{{ formatTime(item.timestamp) }}</span>
                </div>
                <div class="approver-info">
                  <span class="approver">{{ item.approver }}</span>
                  <a-tag
                    :color="getActionColor(item.action)"
                    size="small"
                  >
                    {{ getActionText(item.action) }}
                  </a-tag>
                </div>
                <div v-if="item.comment" class="comment">
                  {{ item.comment }}
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import { Graph } from '@antv/x6'
import { workflowApi } from '@/api/workflow'
import { expenseApi } from '@/api/expense'
import GeneralExpenseClaimDetail from './components/GeneralExpenseClaimDetail.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const flowChartRef = ref()
const approvalFormRef = ref()
const approving = ref(false)
const businessData = ref({})
const flowData = ref({})
const approvalHistory = ref([])
const flowChart = ref(null)

// 审批表单
const approvalForm = reactive({
  comment: '',
  action: ''
})

// 表单验证规则
const approvalRules = {
  comment: [
    {
      validator: (rule, value) => {
        if (approvalForm.action === 'reject' && !value?.trim()) {
          return Promise.reject('驳回时必须填写审批意见')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const detailComponent = computed(() => {
  const businessType = route.query.businessType || 'expense_claim'
  
  switch (businessType) {
    case 'expense_claim':
      return GeneralExpenseClaimDetail
    default:
      return GeneralExpenseClaimDetail
  }
})

// 方法定义
const handleApproval = async () => {
  try {
    approving.value = true
    
    const submitData = {
      instanceId: route.params.id,
      action: approvalForm.action,
      comment: approvalForm.comment
    }
    
    await workflowApi.submitApproval(submitData)
    
    message.success(
      approvalForm.action === 'approve' ? '审批通过' : '已驳回'
    )
    
    // 返回待办列表
    router.push('/dashboard')
    
  } catch (error) {
    message.error('操作失败：' + error.message)
  } finally {
    approving.value = false
  }
}

const handleReject = () => {
  Modal.confirm({
    title: '确认驳回',
    content: '确定要驳回这个申请吗？驳回后需要填写审批意见。',
    okText: '确认驳回',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      approvalForm.action = 'reject'
      approvalFormRef.value?.submit()
    }
  })
}

const getTimelineColor = (action) => {
  switch (action) {
    case 'approve':
      return 'green'
    case 'reject':
      return 'red'
    case 'submit':
      return 'blue'
    default:
      return 'gray'
  }
}

const getActionColor = (action) => {
  switch (action) {
    case 'approve':
      return 'success'
    case 'reject':
      return 'error'
    case 'submit':
      return 'processing'
    default:
      return 'default'
  }
}

const getActionText = (action) => {
  switch (action) {
    case 'approve':
      return '同意'
    case 'reject':
      return '驳回'
    case 'submit':
      return '提交'
    default:
      return '处理中'
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const initFlowChart = () => {
  if (!flowChartRef.value || !flowData.value.nodes) return
  
  nextTick(() => {
    // 初始化流程图
    flowChart.value = new Graph({
      container: flowChartRef.value,
      width: 300,
      height: 400,
      background: {
        color: '#f8f9fa'
      },
      grid: {
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#eee',
            thickness: 1
          },
          {
            color: '#ddd',
            thickness: 1,
            factor: 4
          }
        ]
      }
    })
    
    // 添加节点和边
    const { nodes, edges } = flowData.value
    
    // 添加节点
    nodes.forEach((node, index) => {
      const nodeColor = getNodeColor(node.status)
      
      flowChart.value.addNode({
        id: node.id,
        x: 50,
        y: 50 + index * 80,
        width: 200,
        height: 40,
        shape: 'rect',
        attrs: {
          body: {
            fill: nodeColor.bg,
            stroke: nodeColor.border,
            strokeWidth: 2,
            rx: 6,
            ry: 6
          },
          text: {
            text: node.label,
            fill: nodeColor.text,
            fontSize: 12,
            fontWeight: 'bold'
          }
        },
        data: node
      })
    })
    
    // 添加边
    edges.forEach(edge => {
      flowChart.value.addEdge({
        source: edge.source,
        target: edge.target,
        attrs: {
          line: {
            stroke: '#666',
            strokeWidth: 2,
            targetMarker: {
              name: 'classic',
              size: 8
            }
          }
        }
      })
    })
    
    // 自动布局
    flowChart.value.centerContent()
  })
}

const getNodeColor = (status) => {
  switch (status) {
    case 'completed':
      return {
        bg: '#f6ffed',
        border: '#52c41a',
        text: '#52c41a'
      }
    case 'processing':
      return {
        bg: '#e6f7ff',
        border: '#1890ff',
        text: '#1890ff'
      }
    case 'pending':
      return {
        bg: '#fafafa',
        border: '#d9d9d9',
        text: '#666'
      }
    default:
      return {
        bg: '#fff',
        border: '#d9d9d9',
        text: '#666'
      }
  }
}

const loadData = async () => {
  try {
    const instanceId = route.params.id
    const businessType = route.query.businessType || 'expense_claim'
    const businessId = route.query.businessId
    
    // 加载流程实例数据
    const flowResponse = await workflowApi.getWorkflowInstance(instanceId)
    flowData.value = flowResponse
    
    // 加载审批历史
    const historyResponse = await workflowApi.getApprovalHistory(instanceId)
    approvalHistory.value = historyResponse.data
    
    // 根据业务类型加载业务数据
    if (businessType === 'expense_claim') {
      const businessResponse = await expenseApi.getClaim(businessId)
      businessData.value = businessResponse
    }
    
    // 初始化流程图
    initFlowChart()
    
  } catch (error) {
    message.error('加载数据失败：' + error.message)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.approval-view {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.flow-chart-container {
  height: 400px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.node-name {
  font-weight: 600;
  color: #262626;
}

.timestamp {
  font-size: 12px;
  color: #8c8c8c;
}

.approver-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.approver {
  font-size: 14px;
  color: #595959;
}

.comment {
  font-size: 13px;
  color: #8c8c8c;
  background: #fafafa;
  padding: 8px;
  border-radius: 4px;
  margin-top: 4px;
}
</style>
