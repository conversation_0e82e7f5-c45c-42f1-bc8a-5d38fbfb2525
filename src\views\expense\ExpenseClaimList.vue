<template>
  <div class="expense-claim-list">
    <a-card :bordered="false">
      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="pending">待审批</a-select-option>
              <a-select-option value="approved">已通过</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
              <a-select-option value="paid">已支付</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="申请时间">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item label="关键词">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="报销事由/申请人"
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">
                查询
              </a-button>
              <a-button @click="resetSearch">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
      
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <a-space>
          <a-button
            type="primary"
            @click="$router.push('/expense/claim/new')"
          >
            <plus-outlined />
            新建报销申请
          </a-button>
          
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchDelete"
          >
            <delete-outlined />
            批量删除
          </a-button>
          
          <a-button @click="handleExport">
            <export-outlined />
            导出
          </a-button>
        </a-space>
      </div>
      
      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="claimList"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <a-button
              type="link"
              @click="viewDetail(record)"
            >
              {{ record.title }}
            </a-button>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'totalAmount'">
            <span class="amount-text">¥{{ record.totalAmount?.toFixed(2) }}</span>
          </template>
          
          <template v-else-if="column.key === 'submittedAt'">
            {{ formatTime(record.submittedAt) }}
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                @click="viewDetail(record)"
              >
                查看
              </a-button>
              
              <a-button
                v-if="record.status === 'draft'"
                type="link"
                size="small"
                @click="editClaim(record)"
              >
                编辑
              </a-button>
              
              <a-button
                v-if="['draft', 'pending'].includes(record.status)"
                type="link"
                size="small"
                danger
                @click="deleteClaim(record)"
              >
                删除
              </a-button>
              
              <a-button
                v-if="record.status === 'pending'"
                type="link"
                size="small"
                @click="withdrawClaim(record)"
              >
                撤回
              </a-button>
              
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMenuClick(key, record)">
                    <a-menu-item key="copy">复制申请</a-menu-item>
                    <a-menu-item key="print">打印</a-menu-item>
                    <a-menu-item key="export">导出PDF</a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="报销申请详情"
      width="60%"
      placement="right"
    >
      <general-expense-claim-detail
        v-if="currentClaim.id"
        :data="currentClaim"
        :readonly="true"
      />
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ExportOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { useExpenseStore } from '@/stores/expense'
import GeneralExpenseClaimDetail from './components/GeneralExpenseClaimDetail.vue'

// 路由
const router = useRouter()

// Store
const expenseStore = useExpenseStore()

// 响应式数据
const loading = ref(false)
const detailDrawerVisible = ref(false)
const selectedRowKeys = ref([])
const currentClaim = ref({})

// 搜索表单
const searchForm = reactive({
  status: '',
  dateRange: [],
  keyword: ''
})

// 计算属性
const claimList = computed(() => expenseStore.claimList)
const pagination = computed(() => expenseStore.pagination)

const hasSelected = computed(() => selectedRowKeys.value.length > 0)

const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record) => ({
    disabled: !['draft', 'rejected'].includes(record.status)
  })
}

// 表格列定义
const columns = [
  {
    title: '报销事由',
    key: 'title',
    ellipsis: true,
    width: 200
  },
  {
    title: '申请人',
    dataIndex: 'applicantName',
    width: 100
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    width: 120
  },
  {
    title: '报销金额',
    key: 'totalAmount',
    width: 120,
    align: 'right'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '申请时间',
    key: 'submittedAt',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 方法定义
const handleSearch = () => {
  expenseStore.setSearchParams(searchForm)
  expenseStore.setPagination(1, pagination.value.pageSize)
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    status: '',
    dateRange: [],
    keyword: ''
  })
  expenseStore.resetSearchParams()
  loadData()
}

const handleTableChange = (pag, filters, sorter) => {
  expenseStore.setPagination(pag.current, pag.pageSize)
  loadData()
}

const viewDetail = async (record) => {
  try {
    loading.value = true
    await expenseStore.fetchClaimDetail(record.id)
    currentClaim.value = expenseStore.currentClaim
    detailDrawerVisible.value = true
  } catch (error) {
    message.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

const editClaim = (record) => {
  router.push(`/expense/claim/edit/${record.id}`)
}

const deleteClaim = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除报销申请"${record.title}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await expenseStore.deleteClaim(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const withdrawClaim = (record) => {
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回报销申请"${record.title}"吗？撤回后可重新编辑提交。`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await expenseStore.withdrawClaim(record.id)
        message.success('撤回成功')
        loadData()
      } catch (error) {
        message.error('撤回失败')
      }
    }
  })
}

const handleBatchDelete = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await Promise.all(
          selectedRowKeys.value.map(id => expenseStore.deleteClaim(id))
        )
        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量删除失败')
      }
    }
  })
}

const handleExport = async () => {
  try {
    await expenseStore.exportClaims()
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

const handleMenuClick = (key, record) => {
  switch (key) {
    case 'copy':
      copyClaim(record)
      break
    case 'print':
      printClaim(record)
      break
    case 'export':
      exportClaimPdf(record)
      break
  }
}

const copyClaim = (record) => {
  router.push(`/expense/claim/copy/${record.id}`)
}

const printClaim = (record) => {
  window.open(`/expense/claim/print/${record.id}`, '_blank')
}

const exportClaimPdf = (record) => {
  window.open(`/api/v1/expense-claims/${record.id}/pdf`, '_blank')
}

const getStatusColor = (status) => {
  const colorMap = {
    draft: 'default',
    pending: 'processing',
    approved: 'success',
    rejected: 'error',
    paid: 'success'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status) => {
  const textMap = {
    draft: '草稿',
    pending: '待审批',
    approved: '已通过',
    rejected: '已驳回',
    paid: '已支付'
  }
  return textMap[status] || '未知'
}

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

const loadData = async () => {
  try {
    loading.value = true
    await expenseStore.fetchClaimList()
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.expense-claim-list {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.action-bar {
  margin-bottom: 16px;
}

.amount-text {
  font-weight: 600;
  color: #1890ff;
}
</style>
