<template>
  <div class="general-expense-claim">
    <a-card title="通用报销申请" :bordered="false">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        @finish="handleSubmit"
      >
        <!-- 主信息区 -->
        <a-card title="基本信息" size="small" class="mb-4">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="报销事由" name="claimTitle">
                <a-input 
                  v-model:value="formData.claimTitle" 
                  placeholder="请输入报销事由"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="申请人">
                <a-input 
                  :value="`${userInfo.fullName} (${userInfo.department})`" 
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="收款人类型" name="payeeType">
                <a-radio-group v-model:value="formData.payeeType">
                  <a-radio value="personal">对私</a-radio>
                  <a-radio value="corporate">对公</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收款账户" name="payeeAccount">
                <a-select
                  v-model:value="formData.payeeAccount"
                  placeholder="请选择收款账户"
                  :options="payeeAccountOptions"
                  show-search
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="报销总金额">
                <a-input-number
                  :value="totalAmount"
                  :precision="2"
                  :min="0"
                  disabled
                  style="width: 200px"
                  addon-after="元"
                />
                <span class="ml-2 text-gray-500">由费用明细自动计算</span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 关联信息区 -->
        <a-card title="关联信息" size="small" class="mb-4">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="关联事前申请单">
                <a-select
                  v-model:value="formData.relatedPreApprovalId"
                  placeholder="请选择事前申请单"
                  show-search
                  allow-clear
                  :filter-option="false"
                  :not-found-content="preApprovalLoading ? undefined : null"
                  @search="handlePreApprovalSearch"
                  @change="handlePreApprovalChange"
                >
                  <template v-if="preApprovalLoading" #notFoundContent>
                    <a-spin size="small" />
                  </template>
                  <a-select-option
                    v-for="item in preApprovalOptions"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.title }} - {{ item.amount }}元
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="关联合同">
                <a-select
                  v-model:value="formData.relatedContractId"
                  placeholder="请选择关联合同"
                  show-search
                  allow-clear
                  :filter-option="false"
                  :not-found-content="contractLoading ? undefined : null"
                  @search="handleContractSearch"
                >
                  <template v-if="contractLoading" #notFoundContent>
                    <a-spin size="small" />
                  </template>
                  <a-select-option
                    v-for="item in contractOptions"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }} - {{ item.contractNo }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 费用明细区 -->
        <a-card title="费用明细" size="small" class="mb-4">
          <a-table
            :columns="detailColumns"
            :data-source="formData.details"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'expenseType'">
                <a-select
                  v-model:value="record.expenseType"
                  placeholder="选择费用类型"
                  style="width: 100%"
                  @change="(value) => handleExpenseTypeChange(value, index)"
                >
                  <a-select-option
                    v-for="type in expenseTypes"
                    :key="type.id"
                    :value="type.id"
                  >
                    {{ type.name }}
                  </a-select-option>
                </a-select>
              </template>
              
              <template v-else-if="column.key === 'budgetItem'">
                <a-select
                  v-model:value="record.budgetItemId"
                  placeholder="选择预算科目"
                  style="width: 100%"
                  :disabled="!record.expenseType"
                  @change="(value) => handleBudgetItemChange(value, index)"
                >
                  <a-select-option
                    v-for="item in getBudgetItemsByType(record.expenseType)"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </template>
              
              <template v-else-if="column.key === 'availableBalance'">
                <span :class="{ 'text-red-500': record.availableBalance < record.amount }">
                  {{ record.availableBalance?.toFixed(2) || '-' }}元
                </span>
              </template>
              
              <template v-else-if="column.key === 'amount'">
                <a-input-number
                  v-model:value="record.amount"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                  @change="handleAmountChange"
                />
              </template>
              
              <template v-else-if="column.key === 'description'">
                <a-input
                  v-model:value="record.description"
                  placeholder="费用说明"
                />
              </template>
              
              <template v-else-if="column.key === 'invoiceInfo'">
                <a-button size="small" @click="openInvoiceModal(index)">
                  {{ record.invoiceInfo ? '已录入' : '录入发票' }}
                </a-button>
              </template>
              
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  danger
                  size="small"
                  @click="removeDetail(index)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
          
          <a-button
            type="dashed"
            block
            class="mt-2"
            @click="addDetail"
          >
            <plus-outlined />
            添加费用明细
          </a-button>
        </a-card>

        <!-- 附件区 -->
        <a-card title="附件上传" size="small" class="mb-4">
          <a-upload
            v-model:file-list="formData.attachments"
            name="file"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            @change="handleUploadChange"
          >
            <a-button>
              <upload-outlined />
              上传附件
            </a-button>
          </a-upload>
        </a-card>

        <!-- 审批流程预览区 -->
        <a-card title="审批流程预览" size="small" class="mb-4">
          <a-steps
            :current="0"
            size="small"
            :items="workflowPreview"
          />
        </a-card>

        <!-- 操作按钮 -->
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              提交申请
            </a-button>
            <a-button @click="saveDraft" :loading="saving">
              保存草稿
            </a-button>
            <a-button @click="resetForm">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 发票信息录入弹窗 -->
    <a-modal
      v-model:open="invoiceModalVisible"
      title="发票信息录入"
      width="600px"
      @ok="handleInvoiceOk"
      @cancel="handleInvoiceCancel"
    >
      <a-form :model="currentInvoiceInfo" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="发票代码">
              <a-input v-model:value="currentInvoiceInfo.code" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发票号码">
              <a-input v-model:value="currentInvoiceInfo.number" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开票日期">
              <a-date-picker
                v-model:value="currentInvoiceInfo.date"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发票金额">
              <a-input-number
                v-model:value="currentInvoiceInfo.amount"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="销售方名称">
          <a-input v-model:value="currentInvoiceInfo.sellerName" />
        </a-form-item>
        <a-form-item label="销售方税号">
          <a-input v-model:value="currentInvoiceInfo.sellerTaxNo" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { useExpenseStore } from '@/stores/expense'
import { expenseApi } from '@/api/expense'
import { workflowApi } from '@/api/workflow'

// 使用 stores
const userStore = useUserStore()
const expenseStore = useExpenseStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const saving = ref(false)
const preApprovalLoading = ref(false)
const contractLoading = ref(false)
const invoiceModalVisible = ref(false)
const currentInvoiceIndex = ref(-1)

// 表单数据
const formData = reactive({
  claimTitle: '',
  payeeType: 'personal',
  payeeAccount: null,
  relatedPreApprovalId: null,
  relatedContractId: null,
  details: [],
  attachments: []
})

// 当前发票信息
const currentInvoiceInfo = reactive({
  code: '',
  number: '',
  date: null,
  amount: null,
  sellerName: '',
  sellerTaxNo: ''
})

// 选项数据
const preApprovalOptions = ref([])
const contractOptions = ref([])
const expenseTypes = ref([])
const budgetItems = ref([])
const workflowPreview = ref([])

// 计算属性
const userInfo = computed(() => userStore.userInfo)
const totalAmount = computed(() => {
  return formData.details.reduce((sum, detail) => sum + (detail.amount || 0), 0)
})

const payeeAccountOptions = computed(() => {
  if (formData.payeeType === 'personal') {
    return userStore.personalAccounts?.map(account => ({
      label: `${account.bankName} - ${account.accountNo}`,
      value: account.id
    })) || []
  } else {
    return [] // 对公账户需要手动填写
  }
})

// 表单验证规则
const rules = {
  claimTitle: [
    { required: true, message: '请输入报销事由', trigger: 'blur' }
  ],
  payeeType: [
    { required: true, message: '请选择收款人类型', trigger: 'change' }
  ],
  payeeAccount: [
    { required: true, message: '请选择收款账户', trigger: 'change' }
  ]
}

// 费用明细表格列定义
const detailColumns = [
  {
    title: '费用类型',
    key: 'expenseType',
    width: 150
  },
  {
    title: '预算科目',
    key: 'budgetItem',
    width: 150
  },
  {
    title: '可用余额',
    key: 'availableBalance',
    width: 120
  },
  {
    title: '报销金额',
    key: 'amount',
    width: 120
  },
  {
    title: '费用说明',
    key: 'description',
    width: 200
  },
  {
    title: '发票信息',
    key: 'invoiceInfo',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 上传配置
const uploadUrl = '/api/v1/upload'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

// 方法定义
const handleSubmit = async (values) => {
  try {
    submitting.value = true
    
    // 验证费用明细
    if (formData.details.length === 0) {
      message.error('请至少添加一条费用明细')
      return
    }
    
    // 验证预算余额
    const hasInsufficientBalance = formData.details.some(
      detail => detail.availableBalance < detail.amount
    )
    if (hasInsufficientBalance) {
      message.error('存在费用金额超出预算余额的明细，请检查')
      return
    }
    
    const submitData = {
      ...formData,
      totalAmount: totalAmount.value
    }
    
    await expenseApi.createClaim(submitData)
    message.success('报销申请提交成功')
    resetForm()
  } catch (error) {
    message.error('提交失败：' + error.message)
  } finally {
    submitting.value = false
  }
}

const saveDraft = async () => {
  try {
    saving.value = true
    const draftData = {
      ...formData,
      status: 'draft'
    }
    await expenseApi.saveDraft(draftData)
    message.success('草稿保存成功')
  } catch (error) {
    message.error('保存失败：' + error.message)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    claimTitle: '',
    payeeType: 'personal',
    payeeAccount: null,
    relatedPreApprovalId: null,
    relatedContractId: null,
    details: [],
    attachments: []
  })
}

const addDetail = () => {
  formData.details.push({
    expenseType: null,
    budgetItemId: null,
    availableBalance: null,
    amount: null,
    description: '',
    invoiceInfo: null
  })
}

const removeDetail = (index) => {
  formData.details.splice(index, 1)
  handleAmountChange()
}

const handleExpenseTypeChange = (value, index) => {
  const detail = formData.details[index]
  detail.expenseType = value
  detail.budgetItemId = null
  detail.availableBalance = null
}

const handleBudgetItemChange = async (value, index) => {
  const detail = formData.details[index]
  detail.budgetItemId = value

  // 获取预算余额
  try {
    const response = await expenseApi.getBudgetBalance(value)
    detail.availableBalance = response.data?.availableBalance || 0
  } catch (error) {
    message.error('获取预算余额失败')
    detail.availableBalance = 0
  }
}

const handleAmountChange = () => {
  // 触发总金额重新计算和审批流程预览更新
  updateWorkflowPreview()
}

const getBudgetItemsByType = (expenseTypeId) => {
  return budgetItems.value.filter(item => item.expenseTypeId === expenseTypeId)
}

const handlePreApprovalSearch = async (value) => {
  if (!value) return

  try {
    preApprovalLoading.value = true
    const response = await expenseApi.searchPreApprovals(value)
    preApprovalOptions.value = response.data
  } catch (error) {
    message.error('搜索事前申请单失败')
  } finally {
    preApprovalLoading.value = false
  }
}

const handlePreApprovalChange = async (value) => {
  if (!value) return

  try {
    const preApproval = preApprovalOptions.value.find(item => item.id === value)
    if (preApproval) {
      formData.claimTitle = preApproval.title
    }
  } catch (error) {
    message.error('获取事前申请单详情失败')
  }
}

const handleContractSearch = async (value) => {
  if (!value) return

  try {
    contractLoading.value = true
    const response = await expenseApi.searchContracts(value)
    contractOptions.value = response.data
  } catch (error) {
    message.error('搜索合同失败')
  } finally {
    contractLoading.value = false
  }
}

const openInvoiceModal = (index) => {
  currentInvoiceIndex.value = index
  const detail = formData.details[index]
  if (detail.invoiceInfo) {
    Object.assign(currentInvoiceInfo, detail.invoiceInfo)
  } else {
    Object.assign(currentInvoiceInfo, {
      code: '',
      number: '',
      date: null,
      amount: null,
      sellerName: '',
      sellerTaxNo: ''
    })
  }
  invoiceModalVisible.value = true
}

const handleInvoiceOk = () => {
  const detail = formData.details[currentInvoiceIndex.value]
  detail.invoiceInfo = { ...currentInvoiceInfo }
  invoiceModalVisible.value = false
}

const handleInvoiceCancel = () => {
  invoiceModalVisible.value = false
}

const beforeUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    message.error('只能上传 JPG/PNG/PDF 格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }

  return true
}

const handleUploadChange = (info) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 文件上传成功`)
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 文件上传失败`)
  }
}

const updateWorkflowPreview = async () => {
  if (totalAmount.value <= 0) {
    workflowPreview.value = []
    return
  }

  try {
    const response = await workflowApi.getWorkflowPreview({
      type: 'general',
      amount: totalAmount.value
    })
    workflowPreview.value = response.steps.map(step => ({
      title: step.name,
      description: step.assignee
    }))
  } catch (error) {
    console.error('获取审批流程预览失败:', error)
  }
}

// 初始化数据
const initData = async () => {
  try {
    // 获取费用类型
    const expenseTypesResponse = await expenseApi.getExpenseTypes()
    expenseTypes.value = expenseTypesResponse.data

    // 获取预算科目
    const budgetItemsResponse = await expenseApi.getBudgetItems()
    budgetItems.value = budgetItemsResponse.data

    // 获取用户个人账户信息
    await userStore.loadPersonalAccounts()
  } catch (error) {
    message.error('初始化数据失败')
  }
}

// 监听总金额变化，更新审批流程预览
watch(totalAmount, () => {
  updateWorkflowPreview()
}, { immediate: false })

// 组件挂载时初始化数据
onMounted(() => {
  initData()
})
</script>

<style scoped>
.general-expense-claim {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-red-500 {
  color: #ef4444;
}
</style>
