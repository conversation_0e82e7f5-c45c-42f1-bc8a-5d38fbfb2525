<template>
  <div class="expense-claim-detail">
    <!-- 基本信息 -->
    <a-descriptions
      title="基本信息"
      :column="2"
      bordered
      size="small"
      class="mb-4"
    >
      <a-descriptions-item label="报销事由">
        {{ data.title }}
      </a-descriptions-item>
      <a-descriptions-item label="申请人">
        {{ data.applicantName }} ({{ data.departmentName }})
      </a-descriptions-item>
      <a-descriptions-item label="申请时间">
        {{ formatTime(data.submittedAt) }}
      </a-descriptions-item>
      <a-descriptions-item label="报销总金额">
        <span class="amount-text">¥{{ data.totalAmount?.toFixed(2) }}</span>
      </a-descriptions-item>
      <a-descriptions-item label="收款人类型">
        <a-tag :color="data.payeeType === 'personal' ? 'blue' : 'green'">
          {{ data.payeeType === 'personal' ? '对私' : '对公' }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="收款账户">
        {{ data.payeeAccount?.bankName }} - {{ data.payeeAccount?.accountNo }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 关联信息 -->
    <a-descriptions
      v-if="data.relatedPreApproval || data.relatedContract"
      title="关联信息"
      :column="2"
      bordered
      size="small"
      class="mb-4"
    >
      <a-descriptions-item
        v-if="data.relatedPreApproval"
        label="关联事前申请"
      >
        <a-button
          type="link"
          size="small"
          @click="viewPreApproval(data.relatedPreApproval.id)"
        >
          {{ data.relatedPreApproval.title }}
        </a-button>
      </a-descriptions-item>
      <a-descriptions-item
        v-if="data.relatedContract"
        label="关联合同"
      >
        <a-button
          type="link"
          size="small"
          @click="viewContract(data.relatedContract.id)"
        >
          {{ data.relatedContract.name }}
        </a-button>
      </a-descriptions-item>
    </a-descriptions>

    <!-- 费用明细 -->
    <a-card title="费用明细" size="small" class="mb-4">
      <a-table
        :columns="detailColumns"
        :data-source="data.details"
        :pagination="false"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'expenseType'">
            {{ record.expenseTypeName }}
          </template>
          
          <template v-else-if="column.key === 'budgetItem'">
            {{ record.budgetItemName }}
          </template>
          
          <template v-else-if="column.key === 'amount'">
            <span class="amount-text">¥{{ record.amount?.toFixed(2) }}</span>
          </template>
          
          <template v-else-if="column.key === 'invoiceInfo'">
            <a-button
              v-if="record.invoiceInfo"
              type="link"
              size="small"
              @click="viewInvoice(record.invoiceInfo)"
            >
              查看发票
            </a-button>
            <span v-else class="text-gray">无发票</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 附件信息 -->
    <a-card
      v-if="data.attachments && data.attachments.length > 0"
      title="附件信息"
      size="small"
      class="mb-4"
    >
      <a-list
        :data-source="data.attachments"
        size="small"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-button
                type="link"
                size="small"
                @click="downloadAttachment(item)"
              >
                下载
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="previewAttachment(item)"
              >
                预览
              </a-button>
            </template>
            
            <a-list-item-meta>
              <template #title>
                <file-text-outlined />
                {{ item.originalName }}
              </template>
              <template #description>
                大小: {{ formatFileSize(item.size) }} | 
                上传时间: {{ formatTime(item.uploadTime) }}
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 发票详情弹窗 -->
    <a-modal
      v-model:open="invoiceModalVisible"
      title="发票详情"
      width="600px"
      :footer="null"
    >
      <a-descriptions
        :column="2"
        bordered
        size="small"
      >
        <a-descriptions-item label="发票代码">
          {{ currentInvoice.code }}
        </a-descriptions-item>
        <a-descriptions-item label="发票号码">
          {{ currentInvoice.number }}
        </a-descriptions-item>
        <a-descriptions-item label="开票日期">
          {{ formatDate(currentInvoice.date) }}
        </a-descriptions-item>
        <a-descriptions-item label="发票金额">
          <span class="amount-text">¥{{ currentInvoice.amount?.toFixed(2) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="销售方名称" :span="2">
          {{ currentInvoice.sellerName }}
        </a-descriptions-item>
        <a-descriptions-item label="销售方税号" :span="2">
          {{ currentInvoice.sellerTaxNo }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 附件预览弹窗 -->
    <a-modal
      v-model:open="previewModalVisible"
      title="附件预览"
      width="80%"
      :footer="null"
      centered
    >
      <div class="preview-container">
        <img
          v-if="isImageFile(currentAttachment)"
          :src="currentAttachment.url"
          :alt="currentAttachment.originalName"
          class="preview-image"
        />
        <iframe
          v-else-if="isPdfFile(currentAttachment)"
          :src="currentAttachment.url"
          class="preview-pdf"
        />
        <div v-else class="preview-unsupported">
          <file-text-outlined style="font-size: 48px; color: #ccc;" />
          <p>该文件类型不支持预览，请下载后查看</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { FileTextOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const invoiceModalVisible = ref(false)
const previewModalVisible = ref(false)
const currentInvoice = reactive({})
const currentAttachment = reactive({})

// 费用明细表格列定义
const detailColumns = [
  {
    title: '费用类型',
    key: 'expenseType',
    width: 120
  },
  {
    title: '预算科目',
    key: 'budgetItem',
    width: 150
  },
  {
    title: '报销金额',
    key: 'amount',
    width: 100,
    align: 'right'
  },
  {
    title: '费用说明',
    key: 'description',
    ellipsis: true
  },
  {
    title: '发票信息',
    key: 'invoiceInfo',
    width: 100,
    align: 'center'
  }
]

// 方法定义
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatFileSize = (size) => {
  if (!size) return '-'
  
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(1)} ${units[index]}`
}

const viewInvoice = (invoiceInfo) => {
  Object.assign(currentInvoice, invoiceInfo)
  invoiceModalVisible.value = true
}

const viewPreApproval = (id) => {
  // 跳转到事前申请详情页
  window.open(`/pre-approval/${id}`, '_blank')
}

const viewContract = (id) => {
  // 跳转到合同详情页
  window.open(`/contract/${id}`, '_blank')
}

const downloadAttachment = (attachment) => {
  const link = document.createElement('a')
  link.href = attachment.url
  link.download = attachment.originalName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const previewAttachment = (attachment) => {
  Object.assign(currentAttachment, attachment)
  previewModalVisible.value = true
}

const isImageFile = (file) => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const extension = file.originalName?.split('.').pop()?.toLowerCase()
  return imageTypes.includes(extension)
}

const isPdfFile = (file) => {
  const extension = file.originalName?.split('.').pop()?.toLowerCase()
  return extension === 'pdf'
}
</script>

<style scoped>
.expense-claim-detail {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.amount-text {
  font-weight: 600;
  color: #1890ff;
}

.text-gray {
  color: #8c8c8c;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.preview-image {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
}

.preview-pdf {
  width: 100%;
  height: 600px;
  border: none;
}

.preview-unsupported {
  text-align: center;
  color: #8c8c8c;
}

.preview-unsupported p {
  margin-top: 16px;
  font-size: 16px;
}
</style>
