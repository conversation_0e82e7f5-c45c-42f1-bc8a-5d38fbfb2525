<template>
  <div class="pending-task-list">
    <a-card :bordered="false">
      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="待办总数"
                :value="statistics.pendingCount"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="今日新增"
                :value="todayTasks.length"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="紧急任务"
                :value="urgentTasks.length"
                :value-style="{ color: '#faad14' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="超期任务"
                :value="overdueeTasks.length"
                :value-style="{ color: '#ff4d4f' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>
      
      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form
          :model="searchForm"
          layout="inline"
          @finish="handleSearch"
        >
          <a-form-item label="业务类型">
            <a-select
              v-model:value="searchForm.businessType"
              placeholder="请选择业务类型"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="expense_claim">报销申请</a-select-option>
              <a-select-option value="pre_approval">事前申请</a-select-option>
              <a-select-option value="contract">合同审批</a-select-option>
              <a-select-option value="purchase">采购申请</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="提交时间">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item label="关键词">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="申请人/标题"
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">
                查询
              </a-button>
              <a-button @click="resetSearch">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
      
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <a-space>
          <a-button
            type="primary"
            :disabled="!hasSelected"
            @click="handleBatchApproval"
          >
            <check-outlined />
            批量同意
          </a-button>
          
          <a-button
            :disabled="!hasSelected"
            @click="handleBatchDelegate"
          >
            <user-outlined />
            批量委托
          </a-button>
          
          <a-button @click="refreshData">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </div>
      
      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="pendingTasks"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <div class="task-title">
              <a-button
                type="link"
                @click="handleApproval(record)"
              >
                {{ record.title }}
              </a-button>
              <a-tag
                v-if="record.isUrgent"
                color="red"
                size="small"
                class="ml-2"
              >
                紧急
              </a-tag>
              <a-tag
                v-if="isOverdue(record)"
                color="orange"
                size="small"
                class="ml-2"
              >
                超期
              </a-tag>
            </div>
          </template>
          
          <template v-else-if="column.key === 'businessType'">
            <a-tag :color="getBusinessTypeColor(record.businessType)">
              {{ getBusinessTypeText(record.businessType) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'amount'">
            <span v-if="record.amount" class="amount-text">
              ¥{{ record.amount?.toFixed(2) }}
            </span>
            <span v-else>-</span>
          </template>
          
          <template v-else-if="column.key === 'submittedAt'">
            {{ formatTime(record.submittedAt) }}
          </template>
          
          <template v-else-if="column.key === 'deadline'">
            <span
              :class="{
                'text-red': isOverdue(record),
                'text-orange': isNearDeadline(record)
              }"
            >
              {{ formatTime(record.deadline) }}
            </span>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="primary"
                size="small"
                @click="handleApproval(record)"
              >
                审批
              </a-button>
              
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMenuClick(key, record)">
                    <a-menu-item key="delegate">委托</a-menu-item>
                    <a-menu-item key="transfer">转办</a-menu-item>
                    <a-menu-item key="urge">催办</a-menu-item>
                    <a-menu-item key="view">查看详情</a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  更多
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 批量委托弹窗 -->
    <a-modal
      v-model:open="delegateModalVisible"
      title="批量委托"
      @ok="handleDelegateOk"
      @cancel="handleDelegateCancel"
    >
      <a-form :model="delegateForm" layout="vertical">
        <a-form-item label="委托给" required>
          <a-select
            v-model:value="delegateForm.delegateeTo"
            placeholder="请选择委托人"
            show-search
            :filter-option="false"
            @search="searchUsers"
          >
            <a-select-option
              v-for="user in userOptions"
              :key="user.id"
              :value="user.id"
            >
              {{ user.name }} ({{ user.department }})
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="委托原因">
          <a-textarea
            v-model:value="delegateForm.reason"
            placeholder="请输入委托原因"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  CheckOutlined,
  UserOutlined,
  ReloadOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { useWorkflowStore } from '@/stores/workflow'

// 路由
const router = useRouter()

// Store
const workflowStore = useWorkflowStore()

// 响应式数据
const loading = ref(false)
const delegateModalVisible = ref(false)
const selectedRowKeys = ref([])
const userOptions = ref([])

// 搜索表单
const searchForm = reactive({
  businessType: '',
  dateRange: [],
  keyword: ''
})

// 委托表单
const delegateForm = reactive({
  delegateeTo: null,
  reason: ''
})

// 计算属性
const pendingTasks = computed(() => workflowStore.pendingTasks)
const statistics = computed(() => workflowStore.statistics)
const urgentTasks = computed(() => workflowStore.urgentTasks)
const overdueeTasks = computed(() => workflowStore.overdueeTasks)
const todayTasks = computed(() => workflowStore.todayTasks)
const pagination = computed(() => workflowStore.pendingPagination)

const hasSelected = computed(() => selectedRowKeys.value.length > 0)

const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 表格列定义
const columns = [
  {
    title: '任务标题',
    key: 'title',
    ellipsis: true,
    width: 250
  },
  {
    title: '业务类型',
    key: 'businessType',
    width: 120,
    align: 'center'
  },
  {
    title: '申请人',
    dataIndex: 'applicantName',
    width: 100
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    width: 120
  },
  {
    title: '金额',
    key: 'amount',
    width: 120,
    align: 'right'
  },
  {
    title: '提交时间',
    key: 'submittedAt',
    width: 150
  },
  {
    title: '截止时间',
    key: 'deadline',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 方法定义
const handleSearch = () => {
  workflowStore.setSearchParams(searchForm)
  workflowStore.setPendingPagination(1, pagination.value.pageSize)
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    businessType: '',
    dateRange: [],
    keyword: ''
  })
  workflowStore.resetSearchParams()
  loadData()
}

const handleTableChange = (pag, filters, sorter) => {
  workflowStore.setPendingPagination(pag.current, pag.pageSize)
  loadData()
}

const handleApproval = (record) => {
  router.push({
    path: `/workflow/approval/${record.instanceId}`,
    query: {
      businessType: record.businessType,
      businessId: record.businessId
    }
  })
}

const handleBatchApproval = () => {
  Modal.confirm({
    title: '确认批量同意',
    content: `确定要批量同意选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await workflowStore.batchApproval({
          instanceIds: selectedRowKeys.value,
          action: 'approve',
          comment: '批量同意'
        })
        message.success('批量审批成功')
        selectedRowKeys.value = []
        loadData()
      } catch (error) {
        message.error('批量审批失败')
      }
    }
  })
}

const handleBatchDelegate = () => {
  delegateModalVisible.value = true
}

const handleDelegateOk = async () => {
  try {
    await workflowStore.delegateApproval({
      instanceIds: selectedRowKeys.value,
      delegateeTo: delegateForm.delegateeTo,
      reason: delegateForm.reason
    })
    message.success('批量委托成功')
    selectedRowKeys.value = []
    delegateModalVisible.value = false
    loadData()
  } catch (error) {
    message.error('批量委托失败')
  }
}

const handleDelegateCancel = () => {
  delegateModalVisible.value = false
  Object.assign(delegateForm, {
    delegateeTo: null,
    reason: ''
  })
}

const handleMenuClick = (key, record) => {
  switch (key) {
    case 'delegate':
      delegateTask(record)
      break
    case 'transfer':
      transferTask(record)
      break
    case 'urge':
      urgeTask(record)
      break
    case 'view':
      viewDetail(record)
      break
  }
}

const delegateTask = (record) => {
  // 单个委托逻辑
  selectedRowKeys.value = [record.id]
  delegateModalVisible.value = true
}

const transferTask = (record) => {
  // 转办逻辑
  router.push(`/workflow/transfer/${record.instanceId}`)
}

const urgeTask = async (record) => {
  try {
    await workflowStore.urgeApproval(record.instanceId, '请尽快处理')
    message.success('催办成功')
  } catch (error) {
    message.error('催办失败')
  }
}

const viewDetail = (record) => {
  router.push({
    path: `/workflow/detail/${record.instanceId}`,
    query: {
      businessType: record.businessType,
      businessId: record.businessId
    }
  })
}

const refreshData = () => {
  loadData()
  workflowStore.fetchStatistics()
}

const searchUsers = (value) => {
  // 搜索用户逻辑
  console.log('搜索用户:', value)
}

const getBusinessTypeColor = (type) => {
  const colorMap = {
    expense_claim: 'blue',
    pre_approval: 'green',
    contract: 'orange',
    purchase: 'purple'
  }
  return colorMap[type] || 'default'
}

const getBusinessTypeText = (type) => {
  const textMap = {
    expense_claim: '报销申请',
    pre_approval: '事前申请',
    contract: '合同审批',
    purchase: '采购申请'
  }
  return textMap[type] || '未知'
}

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

const isOverdue = (record) => {
  if (!record.deadline) return false
  return new Date(record.deadline) < new Date()
}

const isNearDeadline = (record) => {
  if (!record.deadline) return false
  const deadline = new Date(record.deadline)
  const now = new Date()
  const diffHours = (deadline - now) / (1000 * 60 * 60)
  return diffHours > 0 && diffHours <= 24
}

const loadData = async () => {
  try {
    loading.value = true
    await workflowStore.fetchPendingTasks()
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  workflowStore.fetchStatistics()
})
</script>

<style scoped>
.pending-task-list {
  padding: 24px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.action-bar {
  margin-bottom: 16px;
}

.task-title {
  display: flex;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.amount-text {
  font-weight: 600;
  color: #1890ff;
}

.text-red {
  color: #ff4d4f;
}

.text-orange {
  color: #faad14;
}
</style>
