### **文档一：前端功能点设计说明书 (高精度字段级)**

**版本：** 1.0
**日期：** 2025年8月5日
**技术栈：** Vue 3, Vite, Ant Design Vue, Pinia, Axios, AntV/X6

**1. 项目概述**
本文档为“寻甸县第一人民医院运营管理系统”提供详尽的前端功能点和字段级设计。所有设计均严格依据《寻甸县第一人民医院内部控制手册》的流程和《基于内部控制理念的医院运营管理系统建设方案》的功能要求。

**2. 核心模块与页面字段设计**

**模块一：统一支撑平台 (Portal & Base)**

*   **1.1 登录页 (`Login.vue`)**
    *   **功能：** 用户认证入口。
    *   **字段/组件：**
        *   `username` (string): a-input，必填。
        *   `password` (string): a-input-password，必填。
        *   `rememberMe` (boolean): a-checkbox。
        *   `ssoLoginButton` (button): a-button，用于跳转OA单点登录。

*   **1.2 主布局 (`MainLayout.vue`)**
    *   **功能：** 系统主框架。
    *   **区域组件：**
        *   `TopHeader`: 显示医院Logo、消息通知图标 (`a-badge`提示未读数)、用户头像和姓名下拉菜单 (`a-dropdown`)。
        *   `SideMenu`: a-menu组件，根据登录用户的角色动态生成菜单项，高亮当前路由。
        *   `ContentView`: Vue Router的 `<router-view>`，用于渲染主页面内容。

*   **1.3 部门负责人工作台 (`DeptHeadDashboard.vue`)**
    *   **功能：** 为部门负责人提供核心工作入口和数据概览。
    *   **组件与数据字段：**
        *   **待我审批列表 (`a-card` + `a-list`):**
            *   `listDataSource` (array of objects):
                *   `{ type: string }` (e.g., '报销申请', '合同审批')
                *   `{ applicant: string }` (申请人)
                *   `{ summary: string }` (摘要，如'张三的差旅报销')
                *   `{ amount: number }` (涉及金额)
                *   `{ submissionTime: datetime }` (提交时间)
                *   `{ link: string }` (跳转到审批页面的路由)
        *   **预算概览仪表盘 (`a-card` + `AntV/G2`):**
            *   **G2仪表盘组件:**
                *   `totalBudget` (number): 部门年度总预算。
                *   `usedBudget` (number): 已使用金额。
                *   `frozenBudget` (number): 冻结金额。
                *   `availableBudget` (number): `computed` 属性，`total - used - frozen`。

**模块二：支出控制管理 (核心交互)**

*   **2.1 通用报销申请页 (`GeneralExpenseClaim.vue`)**
    *   **功能：** 员工提交日常费用的核心页面，体现了系统的智能化和流程化。
    *   **UI组件：** a-form, a-modal, a-table (可编辑模式)。
    *   **表单字段（`formData` in Pinia store）:**
        *   **主信息区:**
            *   `claimTitle` (string): a-input, 报销事由，必填。
            *   `applicantId/Name/Dept` (string): 只读，自动填充。
            *   `totalAmount` (number): a-input-number, 只读，由下方明细自动计算汇总。
            *   `payeeType` (enum: 'personal', 'corporate'): a-radio-group, 收款人类型（对私/对公）。
            *   `payeeAccount` (object): a-select, 根据收款人类型联动，对私时显示个人银行卡列表，对公时需要手动填写。
        *   **关联信息区:**
            *   `relatedPreApprovalId` (string): a-select (远程搜索), 关联事前申请单。选择后自动填充`claimTitle`并锁定部分金额。
            *   `relatedContractId` (string): a-select (远程搜索), 关联合同。
        *   **费用明细区 (`a-table`，`dataSource`可编辑):**
            *   `details` (array of objects):
                *   `{ expenseType: string }` (a-select, 费用类型, e.g., '办公费', '招待费').
                *   `{ budgetItemId: string }` (a-select, 预算科目, 联动费用类型).
                *   `{ availableBalance: number }` (只读文本, 选择预算科目后**实时异步获取**并显示可用余额).
                *   `{ amount: number }` (a-input-number, 报销金额, 输入时实时校验是否超预算).
                *   `{ description: string }` (a-input, 费用说明).
                *   `{ invoiceInfo: object }` (组件, 点击后弹窗 (`a-modal`) 录入或OCR识别发票信息).
        *   **附件区:**
            *   `attachments` (array of file objects): `a-upload`组件。
        *   **审批流程预览区 (只读):**
            *   `workflowPreview` (component): `a-steps`组件，根据报销金额和类型，**前端预请求后端API** (`GET /api/workflow/preview?type=general&amount=...`)，显示预计的审批流程节点。

*   **2.2 审批页面 (`ApprovalView.vue`)**
    *   **功能：** 审批人处理待办事项的统一界面。
    *   **布局：** 左右分栏。左侧为业务单据详情（只读），右侧为审批操作区和历史记录。
    *   **字段/组件：**
        *   **左侧-单据详情:**
            *   动态组件 (`<component :is="...">`) 根据单据类型加载不同的详情组件（如`GeneralExpenseClaimDetail.vue`）。
        *   **右侧-审批操作与历史:**
            *   **流程图 (`a-card` + `AntV/X6`):**
                *   **`flowData` (object):** 从后端 (`GET /api/workflow/instance/{id}`) 获取的节点和边数据，用于渲染图形化流程，清晰展示已完成、进行中、未开始的节点。
            *   **审批操作区 (`a-card`):**
                *   `approvalComment` (string): a-textarea, 审批意见。
                *   `approveButton` (button): a-button (type="primary"), "同意"。
                *   `rejectButton` (button): a-button (type="danger"), "驳回"。点击后要求必须填写审批意见。
            *   **审批历史 (`a-card` + `a-timeline`):**
                *   `history` (array of objects):
                    *   `{ nodeName: string }` (节点名称)
                    *   `{ approver: string }` (审批人)
                    *   `{ action: string }` (操作, '同意'/'驳回')
                    *   `{ comment: string }` (意见)
                    *   `{ timestamp: datetime }` (时间)

---

### **文档二：后端功能点设计说明书 (高精度字段级)**

**版本：** 1.0
**日期：** 2025年8月5日
**技术栈：** Go, Gin, PostgreSQL, GORM, Zap, Viper

**1. 项目概述**
本文档详细定义了“寻甸县第一人民医院运营管理系统”后端服务的功能、API接口、核心数据模型及其字段。后端负责系统的所有业务逻辑、数据持久化和流程驱动。

**2. 数据库核心表结构设计 (PostgreSQL)**

*   **`users` (用户信息表)**
    *   `id` (bigserial, PK), `username` (varchar, unique), `password_hash` (varchar), `full_name` (varchar), `department_id` (bigint, FK), `employee_id` (varchar), `title` (varchar, 职级), `roles` (jsonb), `created_at` (timestamptz), `updated_at` (timestamptz).

*   **`budget_items` (预算项表)**
    *   `id` (bigserial, PK), `year` (integer), `department_id` (bigint), `subject_code` (varchar), `subject_name` (varchar), `total_amount` (numeric), `used_amount` (numeric, default 0), `frozen_amount` (numeric, default 0), `control_type` (varchar, 'rigid'/'flexible').

*   **`budget_freezes` (预算冻结记录表)**
    *   `id` (bigserial, PK), `budget_item_id` (bigint, FK), `amount` (numeric), `business_id` (bigint), `business_type` (varchar, e.g., 'pre_approval', 'contract'), `status` (varchar, 'active'/'released'/'used'), `created_at` (timestamptz).

*   **`expense_claims` (报销单主表)**
    *   `id` (bigserial, PK), `title` (varchar), `applicant_id` (bigint, FK), `department_id` (bigint), `total_amount` (numeric), `status` (varchar, e.g., 'draft', 'pending', 'approved', 'rejected', 'paid'), `workflow_instance_id` (varchar), `related_pre_approval_id` (bigint), `related_contract_id` (bigint), `submitted_at` (timestamptz).

*   **`expense_claim_details` (报销单明细表)**
    *   `id` (bigserial, PK), `claim_id` (bigint, FK), `expense_type_id` (bigint), `budget_item_id` (bigint, FK), `amount` (numeric), `description` (text), `invoice_data` (jsonb).

*   **`workflow_instances` (流程实例表)**
    *   `id` (bigserial, PK), `definition_key` (varchar), `business_id` (bigint), `business_type` (varchar), `current_node_id` (varchar), `status` (varchar), `start_user_id` (bigint), `start_time` (timestamptz), `end_time` (timestamptz).

*   **`workflow_approval_history` (审批历史记录表)**
    *   `id` (bigserial, PK), `instance_id` (bigint, FK), `node_id` (varchar), `node_name` (varchar), `approver_id` (bigint), `action` (varchar, 'approve'/'reject'), `comment` (text), `created_at` (timestamptz).

**3. 后端API接口设计**

**模块一：认证与授权 (Package: `auth`)**
*   **`POST /api/v1/login`**
    *   **Request Body:** `{ "username": "...", "password": "..." }`
    *   **Response (200 OK):** `{ "token": "...", "userInfo": { ... } }`
    *   **Response (401 Unauthorized):** `{ "error": "Invalid credentials" }`

**模块二：支出控制 (Package: `expense`)**
*   **`POST /api/v1/expense-claims`**
    *   **功能：** 提交一个新的通用报销申请。
    *   **JWT:** Required.
    *   **Request Body:**
        ```json
        {
          "title": "购买办公用品",
          "payeeType": "corporate",
          "payeeInfo": { "name": "...", "bank": "...", "account": "..." },
          "relatedPreApprovalId": 123,
          "details": [
            {
              "expenseTypeId": 1,
              "budgetItemId": 45,
              "amount": 500.00,
              "description": "A4纸一批",
              "invoiceData": { "code": "...", "number": "..." }
            }
          ],
          "attachments": [ { "fileId": "xyz..." } ]
        }
        ```    *   **核心逻辑：**
        1.  开启数据库事务。
        2.  从JWT中获取`applicantId`。
        3.  校验`budgetItemId`是否存在且属于该用户的部门。
        4.  **调用预算服务进行金额冻结 `budgetSvc.Freeze(...)`**。
        5.  创建`expense_claims`和`expense_claim_details`记录。
        6.  **调用工作流服务启动流程 `workflowSvc.Start(...)`**。
        7.  将返回的`workflow_instance_id`更新回报销单主表。
        8.  提交事务。
    *   **Response (201 Created):** `{ "claimId": 789, "status": "pending" }`

*   **`GET /api/v1/expense-claims/{id}`**
    *   **功能：** 获取指定报销单的完整详情。
    *   **JWT:** Required.
    *   **Response (200 OK):** 返回包含主信息、明细、附件、**关联的流程实例状态**的完整报销单对象。

**模块三：工作流 (Package: `workflow`)**
*   **`POST /api/v1/approvals`**
    *   **功能:** 审批人提交审批决定。
    *   **JWT:** Required.
    *   **Request Body:**
        ```json
        {
          "instanceId": 56,
          "action": "approve", // or "reject"
          "comment": "情况属实，同意报销。"
        }
        ```
    *   **核心逻辑:**
        1.  开启数据库事务。
        2.  从JWT获取审批人`approverId`。
        3.  验证`approverId`是否有权限审批`instanceId`的当前节点。
        4.  在`workflow_approval_history`中创建一条记录。
        5.  **如果同意:**
            *   根据流程定义，将`workflow_instances`的`current_node_id`更新到下一节点。
            *   如果已是最后节点，更新`instanceId`状态为'approved'，并**触发业务回调**（如调用 `expenseSvc.HandleApprovalPassed(businessId)`）。
        6.  **如果驳回:**
            *   更新`instanceId`状态为'rejected'，并**触发业务回调**（如调用 `expenseSvc.HandleApprovalRejected(businessId)`）。
        7.  提交事务。
        8.  （异步）发送消息通知。
    *   **Response (200 OK):** `{ "success": true }`

*   **`GET /api/v1/workflow/instance/{id}`**
    *   **功能:** 获取流程实例的图形化展示所需数据。
    *   **JWT:** Required.
    *   **Response (200 OK):**
        ```json
        {
          "nodes": [
            { "id": "start", "label": "发起申请", "status": "completed", "assignee": "张三" },
            { "id": "dept_head", "label": "部门负责人审批", "status": "processing", "assignee": "李四" },
            { "id": "finance", "label": "财务审核", "status": "pending", "assignee": "财务科" }
          ],
          "edges": [
            { "source": "start", "target": "dept_head" },
            { "source": "dept_head", "target": "finance" }
          ]
        }
        ```
