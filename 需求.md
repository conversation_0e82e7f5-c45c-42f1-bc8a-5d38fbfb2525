 **前端 Vue 3 + Ant Design Vue** 和 **后端 Go + PostgreSQL** 来重新设计这套系统。这是一个非常现代化、高性能的技术组合，非常适合构建响应迅速、稳定可靠的企业级应用。

我将以软件开发者的身份，为您提供一份全新的、基于这个技术栈的详细架构设计和实施方案。

---

### **寻甸县第一人民医院运营管理系统建设方案 (Vue 3 + Go 定制版)**

#### **第一章：核心需求与技术栈总览 (保持不变)**

项目的核心目标依然是将《内控手册》信息化，实现以**预算为主线、资金管控为核心**的业务闭环。功能需求（预算、支出、采购、合同、移动端等）保持不变。

**技术栈确定：**

*   **前端：**
    *   **核心框架：** Vue 3 (使用组合式API，提升代码组织能力和性能)
    *   **UI 组件库：** Ant Design Vue (提供企业级、设计精美的现成组件)
    *   **构建工具：** Vite (极速的开发服务器和构建体验)
    *   **状态管理：** Pinia (Vue 3 官方推荐，更简洁、更直观的状态管理)
    *   **路由：** Vue Router
    *   **HTTP 请求：** Axios
    *   **数据可视化 (用于驾驶舱):** AntV/G2 (与Ant Design一脉相承，专业图表库)
    *   **移动端：** uni-app (一套代码，多端发布，完美契合需求)

*   **后端：**
    *   **核心语言/框架：** Go (使用标准库 + Gin/Go-Zero Web框架)
    *   **数据库：** PostgreSQL 14+
    *   **ORM / 数据库驱动：** GORM (功能强大的ORM库) 或 sqlx (轻量级扩展)
    *   **配置管理：** Viper
    *   **日志：** Zap
    *   **API 文档：** Swagger (通过Go-Swagger自动生成)
    *   **容器化：** Docker & Docker Compose
    *   **CI/CD：** Jenkins / GitLab CI

#### **第二章：前端设计方案 (Vue 3 + Ant Design Vue)**

前端设计理念不变，依然是 **“角色化、流程化、可视化”**，但具体实现细节将围绕Vue 3生态展开。

**1. 页面结构与组件化设计**

*   **布局 (Layout):** 使用Ant Design Vue的 `a-layout` 组件构建经典的后台管理界面（顶部导航/Logo区、左侧菜单栏、主内容区）。
*   **菜单：** 左侧菜单栏 (`a-menu`) 根据用户的角色权限动态生成。
*   **页面：** 每个核心功能模块（如“预算编审”、“我的报销单”）都作为一个独立的Vue页面组件。
*   **组件复用：** 将常用功能封装成可复用的组件。例如：
    *   `FileUpload.vue`: 统一的附件上传组件。
    *   `UserSelector.vue`: 人员选择器（用于选择审批人等）。
    *   `BudgetPicker.vue`: 预算项选择器，内置搜索和预算余额显示。

**2. 核心功能点的前端实现**

*   **① 角色化工作台 (Dashboard):**
    *   **实现：** 利用Vue Router的路由守卫进行权限控制。用户登录后，根据其角色信息，动态添加其有权限访问的路由。`Pinia` store中存储用户的角色和工作台布局配置。工作台上的图表使用 **AntV/G2** 库渲染，动态、交互性强。

*   **② 流程图驱动的审批与追踪 (关键功能):**
    *   **实现：** 这个功能需要一个专门的流程可视化组件。可以基于 **AntV/X6** 或 **LogicFlow** 这样的流程图编辑库来定制。
    *   **数据流：**
        1.  进入一个包含流程的页面时，向后端请求该流程实例的节点信息（`GET /api/workflow/instance/{id}`）。
        2.  后端返回一个包含节点列表和连线信息的JSON数据。
        3.  前端的流程图组件解析这个JSON，渲染出图形化的流程图。
        4.  节点的`status`字段（如 `completed`, `processing`, `pending`）决定了其在图上的样式（颜色、图标）。
        5.  鼠标悬浮在已完成的节点上时，弹出一个 `a-popover` 或 `a-tooltip`，显示审批详情（审批人、时间、意见）。

*   **③ 智能填单与校验：**
    *   **实现：** 大量使用Vue 3的 **`watch`** 和 **`computed`** 特性来实现数据联动。
    *   **场景示例（报销单）：**
        *   用户选择“费用类型” (`a-select`) 后，`watch`该值的变化，立即触发一个异步请求，获取并筛选出该费用类型可用的“预算科目”，更新预算科目下拉框的内容。
        *   用户输入金额 (`a-input-number`) 时，利用 `debounce`（防抖）技术，在用户停止输入的`300ms`后，自动向后端发送预算校验请求，并将返回的可用余额显示在输入框旁边。
        *   表单提交时，使用Ant Design Vue的 `a-form` 自带的校验规则 (`rules`)，结合自定义校验函数，对所有字段进行前端校验，不通过则阻止提交。

#### **第三章：后端设计方案 (Go + PostgreSQL)**

Go语言以其高性能、高并发和简洁的语法而著称。我们将采用清晰、可维护的Go项目结构来构建后端服务。

**1. 架构模式：单体应用 vs 微服务**

*   **建议：** 初期采用 **“模块化的单体应用” (Modular Monolith)**。即在一个Go项目中，将不同业务（用户、预算、支出等）按`package`清晰地划分开，每个`package`内部高内聚。
*   **理由：** 相比直接上微服务，这种方式在项目初期开发、部署和维护更简单，能快速迭代。Go的性能足以应对初期到中期的负载。当未来业务量激增，可以平滑地将这些独立的`package`拆分成真正的微服务。

**2. 核心模块与Go Package设计**

```
hospital-imc-api/
├── api/             # API接口定义 (Swagger/OpenAPI)
├── cmd/             # main函数入口
│   └── server/
│       └── main.go
├── config/          # 配置文件 (config.yaml)
├── internal/        # 核心业务逻辑 (不对外暴露)
│   ├── handler/     # HTTP处理器 (Gin handlers)
│   │   ├── user.go
│   │   ├── budget.go
│   │   └── expense.go
│   ├── model/       # 数据库模型 (GORM structs)
│   │   ├── user.go
│   │   ├── budget.go
│   │   └── expense.go
│   ├── repository/  # 数据仓库层 (封装数据库操作)
│   │   ├── user_repo.go
│   │   ├── budget_repo.go
│   │   └── expense_repo.go
│   ├── service/     # 业务逻辑服务层
│   │   ├── user_service.go
│   │   ├── budget_service.go
│   │   └── expense_service.go
│   └── middleware/  # 中间件 (如鉴权)
├── pkg/             # 可复用的公共库 (如utils, response)
└── go.mod           # Go模块依赖
```

**3. 核心业务逻辑在Go中的实现**

*   **工作流引擎的替代方案：**
    *   由于Go生态中没有像Java `Flowable` 那样功能完备的图形化工作流引擎，我们可以采用一种**更轻量、更符合Go风格**的方式：**状态机模型 + 数据库驱动**。
    *   **设计思路：**
        1.  **`wf_flow_definitions` 表：** 用JSON或文本存储流程的节点定义（如：`[{ "step": 1, "name": "部门负责人审批", "role": "DEPT_MANAGER" }, { "step": 2, ... }]`）。
        2.  **`wf_flow_instances` 表：** 记录每个运行中的流程实例，包含 `business_id` (业务单据ID), `business_type`, `current_step`, `status` (pending, approved, rejected) 等字段。
        3.  **`wf_approval_nodes` 表：** 记录每个节点的审批历史，包含 `instance_id`, `step`, `approver_id`, `status`, `comment`, `processed_at` 等。
    *   **处理流程 (`service/expense_service.go`):**
        *   当一个审批请求 `approve(instanceId, userId, comment)` 到达时：
            *   开启数据库事务。
            *   验证 `userId` 是否是 `current_step` 的合法审批人。
            *   更新 `wf_approval_nodes` 中对应节点的`status`为 `approved`。
            *   根据流程定义，将 `wf_flow_instances` 的 `current_step` 更新到下一步。如果已是最后一步，则更新整个实例的`status`为 `approved`。
            *   **触发业务操作：** 如果流程审批通过，调用 `budget_service` 的 `UseBudget` 方法。
            *   提交事务。
            *   发送消息通知下一环节审批人或申请人。

*   **预算控制的实现 (`service/budget_service.go`):**
    *   预算操作（冻结、使用、释放）的核心是**保证数据一致性和并发安全**。
    *   **实现：**
        *   使用 **`FOR UPDATE`** SQL语句在事务中锁定预算项的行，防止并发更新导致的数据错乱。
        *   `FreezeBudget` 函数：`UPDATE tbl_budget_items SET frozen_amount = frozen_amount + ? WHERE id = ? AND total_amount - used_amount - frozen_amount >= ?`。通过`WHERE`子句在数据库层面就保证了余额充足，这是一个非常高效和安全的并发控制手段。

#### **第四章：系统架构图 (Vue + Go 版本)**

```mermaid
graph TD
    subgraph 用户端 (Clients)
        WebApp[Vue 3 SPA<br/>(Ant Design Vue)]
        MobileApp[uni-app<br/>(企业微信小程序)]
    end

    subgraph 后端服务 (Backend - Go Monolith)
        GoApp[Go Gin/Go-Zero 应用]
      
        GoApp -- 包含 --> Handler(路由处理器)
        Handler -- 调用 --> Service(业务逻辑层)
        Service -- 调用 --> Repository(数据访问层)
        Repository -- 操作 --> DB[(PostgreSQL 数据库 GORM/sqlx)]
    end
  
    subgraph 基础设施 (Infrastructure)
        DB
        Redis[(Redis<br/>缓存/会话)]
        Nginx[Nginx<br/>反向代理/静态资源]
    end

    subgraph 外部系统集成 (Integration)
        HISInt[HIS系统接口]
        AcctInt[会计系统接口]
        OAInt[OA系统接口]
    end

    User(用户) --> WebApp
    User --> MobileApp

    WebApp -- HTTP/HTTPS --> Nginx
    MobileApp -- HTTP/HTTPS --> Nginx
  
    Nginx -- 路由 --> GoApp

    Service -- 使用 --> Redis

    Service -- 异步/定时任务 --> HISInt
    Service -- 凭证数据 --> AcctInt
    Service -- 用户同步/SSO --> OAInt

```